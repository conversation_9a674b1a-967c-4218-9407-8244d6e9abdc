import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Chip,
} from '@mui/material';
import { Add as AddIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import { dashboardApi, portfolioApi } from '../../services/api';

interface Portfolio {
  id: number;
  name: string;
  initial_capital: number;
  current_value: number;
  cash_balance: number;
  total_returns: number;
  returns_percentage: number;
  holdings_count: number;
}

interface CapitalManagementData {
  portfolios: Portfolio[];
  total_capital: number;
  total_current_value: number;
  total_cash: number;
  overall_returns: number;
}

const CapitalManagement: React.FC = () => {
  const [data, setData] = useState<CapitalManagementData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCapitalData();
  }, []);

  const fetchCapitalData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getCapitalManagement();
      setData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch capital management data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!data) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        No capital management data available
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Capital Management
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchCapitalData}
            sx={{ mr: 2 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
          >
            New Portfolio
          </Button>
        </Box>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Capital
              </Typography>
              <Typography variant="h5">
                {formatCurrency(data.total_capital)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Current Value
              </Typography>
              <Typography variant="h5">
                {formatCurrency(data.total_current_value)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Available Cash
              </Typography>
              <Typography variant="h5">
                {formatCurrency(data.total_cash)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Overall Returns
              </Typography>
              <Typography 
                variant="h5" 
                color={data.overall_returns >= 0 ? 'success.main' : 'error.main'}
              >
                {formatCurrency(data.overall_returns)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Portfolios Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Portfolio Details
          </Typography>
          <TableContainer component={Paper} elevation={0}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Portfolio Name</TableCell>
                  <TableCell align="right">Initial Capital</TableCell>
                  <TableCell align="right">Current Value</TableCell>
                  <TableCell align="right">Cash Balance</TableCell>
                  <TableCell align="right">Returns</TableCell>
                  <TableCell align="right">Returns %</TableCell>
                  <TableCell align="center">Holdings</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.portfolios.map((portfolio) => (
                  <TableRow key={portfolio.id}>
                    <TableCell component="th" scope="row">
                      <Typography variant="subtitle2">
                        {portfolio.name}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(portfolio.initial_capital)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(portfolio.current_value)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(portfolio.cash_balance)}
                    </TableCell>
                    <TableCell 
                      align="right"
                      sx={{ 
                        color: portfolio.total_returns >= 0 ? 'success.main' : 'error.main' 
                      }}
                    >
                      {formatCurrency(portfolio.total_returns)}
                    </TableCell>
                    <TableCell 
                      align="right"
                      sx={{ 
                        color: portfolio.returns_percentage >= 0 ? 'success.main' : 'error.main' 
                      }}
                    >
                      {formatPercentage(portfolio.returns_percentage)}
                    </TableCell>
                    <TableCell align="center">
                      <Chip 
                        label={portfolio.holdings_count} 
                        size="small" 
                        color="primary" 
                        variant="outlined" 
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Button size="small" variant="outlined">
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          {data.portfolios.length === 0 && (
            <Box textAlign="center" py={4}>
              <Typography color="textSecondary">
                No portfolios found. Create your first portfolio to get started.
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default CapitalManagement;
