from sqlalchemy.orm import Session
from typing import Dict, Any, List
from datetime import datetime, timedelta
from app.db.models import Portfolio, Holding, Transaction, Stock, Signal, GTTOrder, BrokerConnection
from app.services.stock_service import StockService
from app.services.portfolio_service import PortfolioService


class DashboardService:
    def __init__(self, db: Session):
        self.db = db
        self.stock_service = StockService(db)
        self.portfolio_service = PortfolioService(db)

    async def get_overview(self) -> Dict[str, Any]:
        """Get dashboard overview data"""
        # Get portfolio summary
        portfolios = self.db.query(Portfolio).filter(Portfolio.is_active == True).all()
        total_portfolio_value = sum(p.current_value for p in portfolios)
        total_returns = sum(p.total_returns for p in portfolios)
        
        # Get active signals count
        active_signals = self.db.query(Signal).filter(Signal.is_active == True).count()
        
        # Get GTT orders count
        active_gtt_orders = self.db.query(GTTOrder).filter(GTTOrder.status == "ACTIVE").count()
        
        # Get broker connection status
        broker_connection = self.db.query(BrokerConnection).first()
        is_broker_connected = broker_connection.is_connected if broker_connection else False
        
        return {
            "total_portfolio_value": total_portfolio_value,
            "total_returns": total_returns,
            "returns_percentage": (total_returns / sum(p.initial_capital for p in portfolios)) * 100 if portfolios else 0,
            "active_signals": active_signals,
            "active_gtt_orders": active_gtt_orders,
            "broker_connected": is_broker_connected,
            "portfolios_count": len(portfolios),
            "last_updated": datetime.now().isoformat()
        }

    async def get_capital_management_data(self) -> Dict[str, Any]:
        """Get capital management dashboard data"""
        portfolios = self.db.query(Portfolio).filter(Portfolio.is_active == True).all()
        
        portfolio_data = []
        for portfolio in portfolios:
            summary = await self.portfolio_service.get_portfolio_summary(portfolio.id)
            portfolio_data.append({
                "id": portfolio.id,
                "name": portfolio.name,
                "initial_capital": portfolio.initial_capital,
                "current_value": portfolio.current_value,
                "cash_balance": portfolio.cash_balance,
                "total_returns": portfolio.total_returns,
                "returns_percentage": portfolio.returns_percentage,
                "holdings_count": summary.holdings_count if summary else 0
            })
        
        return {
            "portfolios": portfolio_data,
            "total_capital": sum(p.initial_capital for p in portfolios),
            "total_current_value": sum(p.current_value for p in portfolios),
            "total_cash": sum(p.cash_balance for p in portfolios),
            "overall_returns": sum(p.total_returns for p in portfolios)
        }

    async def get_stock_universal_data(self) -> Dict[str, Any]:
        """Get stock universal dashboard data"""
        # Get top performing stocks
        stocks = self.db.query(Stock).filter(Stock.is_active == True).limit(50).all()
        
        stock_data = []
        for stock in stocks:
            price_data = await self.stock_service.get_current_price(stock.symbol)
            if 'current_price' in price_data:
                stock_data.append({
                    "symbol": stock.symbol,
                    "name": stock.name,
                    "current_price": price_data['current_price'],
                    "previous_close": price_data['previous_close'],
                    "change": price_data['current_price'] - price_data['previous_close'],
                    "change_percentage": ((price_data['current_price'] - price_data['previous_close']) / price_data['previous_close']) * 100,
                    "volume": price_data['volume'],
                    "market_cap": price_data['market_cap']
                })
        
        # Sort by change percentage
        stock_data.sort(key=lambda x: x['change_percentage'], reverse=True)
        
        return {
            "top_gainers": stock_data[:10],
            "top_losers": stock_data[-10:],
            "all_stocks": stock_data,
            "market_summary": {
                "total_stocks": len(stock_data),
                "gainers": len([s for s in stock_data if s['change'] > 0]),
                "losers": len([s for s in stock_data if s['change'] < 0]),
                "unchanged": len([s for s in stock_data if s['change'] == 0])
            }
        }

    async def get_boh_filter_data(self) -> Dict[str, Any]:
        """Get BOH filter dashboard data"""
        # Placeholder for BOH filter logic
        return {
            "message": "BOH Filter data - logic to be implemented",
            "filters": [],
            "results": [],
            "last_updated": datetime.now().isoformat()
        }

    async def get_weekly_high_data(self) -> Dict[str, Any]:
        """Get weekly high dashboard data"""
        # Get stocks near weekly highs
        stocks = self.db.query(Stock).filter(Stock.is_active == True).limit(20).all()
        
        weekly_high_stocks = []
        for stock in stocks:
            # Get weekly data
            price_history = await self.stock_service.get_price_history(stock.symbol, period="1mo", interval="1d")
            if 'data' in price_history and price_history['data']:
                recent_data = price_history['data'][-7:]  # Last 7 days
                weekly_high = max(day['high'] for day in recent_data)
                current_price = recent_data[-1]['close']
                
                # Check if current price is within 5% of weekly high
                if current_price >= weekly_high * 0.95:
                    weekly_high_stocks.append({
                        "symbol": stock.symbol,
                        "name": stock.name,
                        "current_price": current_price,
                        "weekly_high": weekly_high,
                        "distance_from_high": ((weekly_high - current_price) / weekly_high) * 100
                    })
        
        return {
            "weekly_high_stocks": weekly_high_stocks,
            "total_candidates": len(weekly_high_stocks),
            "last_updated": datetime.now().isoformat()
        }

    async def get_weekly_high_signals(self) -> Dict[str, Any]:
        """Get weekly high signal generation data"""
        signals = self.db.query(Signal).filter(Signal.is_active == True).order_by(Signal.created_at.desc()).limit(20).all()
        
        signal_data = []
        for signal in signals:
            stock = self.db.query(Stock).filter(Stock.id == signal.stock_id).first()
            signal_data.append({
                "id": signal.id,
                "stock_symbol": stock.symbol if stock else "Unknown",
                "stock_name": stock.name if stock else "Unknown",
                "signal_type": signal.signal_type,
                "signal_strength": signal.signal_strength,
                "price": signal.price,
                "signal_date": signal.signal_date.isoformat(),
                "created_at": signal.created_at.isoformat()
            })
        
        return {
            "signals": signal_data,
            "buy_signals": len([s for s in signal_data if s['signal_type'] == 'BUY']),
            "sell_signals": len([s for s in signal_data if s['signal_type'] == 'SELL']),
            "total_signals": len(signal_data)
        }

    async def get_gtt_orders(self) -> Dict[str, Any]:
        """Get GTT order management data"""
        gtt_orders = self.db.query(GTTOrder).order_by(GTTOrder.created_at.desc()).limit(50).all()
        
        order_data = []
        for order in gtt_orders:
            stock = self.db.query(Stock).filter(Stock.id == order.stock_id).first()
            order_data.append({
                "id": order.id,
                "stock_symbol": stock.symbol if stock else "Unknown",
                "order_type": order.order_type,
                "trigger_price": order.trigger_price,
                "quantity": order.quantity,
                "price": order.price,
                "status": order.status,
                "created_at": order.created_at.isoformat(),
                "expires_at": order.expires_at.isoformat() if order.expires_at else None
            })
        
        return {
            "gtt_orders": order_data,
            "active_orders": len([o for o in order_data if o['status'] == 'ACTIVE']),
            "triggered_orders": len([o for o in order_data if o['status'] == 'TRIGGERED']),
            "total_orders": len(order_data)
        }

    async def get_current_holdings(self) -> Dict[str, Any]:
        """Get current holdings data"""
        portfolios = self.db.query(Portfolio).filter(Portfolio.is_active == True).all()
        
        all_holdings = []
        for portfolio in portfolios:
            holdings = self.db.query(Holding).filter(Holding.portfolio_id == portfolio.id).all()
            for holding in holdings:
                stock = self.db.query(Stock).filter(Stock.id == holding.stock_id).first()
                all_holdings.append({
                    "portfolio_name": portfolio.name,
                    "stock_symbol": stock.symbol if stock else "Unknown",
                    "stock_name": stock.name if stock else "Unknown",
                    "quantity": holding.quantity,
                    "average_price": holding.average_price,
                    "current_price": holding.current_price,
                    "market_value": holding.market_value,
                    "unrealized_pnl": holding.unrealized_pnl,
                    "unrealized_pnl_percentage": holding.unrealized_pnl_percentage
                })
        
        return {
            "holdings": all_holdings,
            "total_holdings": len(all_holdings),
            "total_market_value": sum(h['market_value'] for h in all_holdings),
            "total_unrealized_pnl": sum(h['unrealized_pnl'] for h in all_holdings)
        }

    async def get_backtesting_data(self) -> Dict[str, Any]:
        """Get backtesting dashboard data"""
        # Placeholder for backtesting data
        return {
            "message": "Backtesting data - to be implemented",
            "recent_backtests": [],
            "performance_summary": {},
            "last_updated": datetime.now().isoformat()
        }

    async def get_broker_connection_data(self) -> Dict[str, Any]:
        """Get broker connection dashboard data"""
        broker_connection = self.db.query(BrokerConnection).first()
        
        if broker_connection:
            return {
                "broker_name": broker_connection.broker_name,
                "is_connected": broker_connection.is_connected,
                "last_connected": broker_connection.last_connected.isoformat() if broker_connection.last_connected else None,
                "connection_status": broker_connection.connection_status,
                "client_code": broker_connection.client_code
            }
        else:
            return {
                "broker_name": None,
                "is_connected": False,
                "last_connected": None,
                "connection_status": "Not configured",
                "client_code": None
            }

    async def get_market_summary(self) -> Dict[str, Any]:
        """Get market summary for dashboard"""
        # Get major indices data (placeholder)
        return {
            "indices": [
                {"name": "NIFTY 50", "value": 0, "change": 0, "change_percentage": 0},
                {"name": "SENSEX", "value": 0, "change": 0, "change_percentage": 0},
                {"name": "NIFTY BANK", "value": 0, "change": 0, "change_percentage": 0}
            ],
            "market_status": "OPEN",  # OPEN, CLOSED, PRE_OPEN
            "last_updated": datetime.now().isoformat()
        }

    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for dashboard"""
        portfolios = self.db.query(Portfolio).filter(Portfolio.is_active == True).all()
        
        if not portfolios:
            return {
                "total_return": 0,
                "total_return_percentage": 0,
                "best_performing_portfolio": None,
                "worst_performing_portfolio": None,
                "average_return": 0
            }
        
        total_initial = sum(p.initial_capital for p in portfolios)
        total_current = sum(p.current_value for p in portfolios)
        total_return = total_current - total_initial
        total_return_percentage = (total_return / total_initial) * 100 if total_initial > 0 else 0
        
        best_portfolio = max(portfolios, key=lambda p: p.returns_percentage)
        worst_portfolio = min(portfolios, key=lambda p: p.returns_percentage)
        
        return {
            "total_return": total_return,
            "total_return_percentage": total_return_percentage,
            "best_performing_portfolio": {
                "name": best_portfolio.name,
                "return_percentage": best_portfolio.returns_percentage
            },
            "worst_performing_portfolio": {
                "name": worst_portfolio.name,
                "return_percentage": worst_portfolio.returns_percentage
            },
            "average_return": sum(p.returns_percentage for p in portfolios) / len(portfolios)
        }
