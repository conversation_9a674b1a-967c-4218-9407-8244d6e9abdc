import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Slider,
  Switch,
  FormControlLabel,
  Divider,
} from '@mui/material';
import {
  FilterList as FilterIcon,
  PlayArrow as RunIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { dashboardApi } from '../../services/api';

interface BOHFilterData {
  message: string;
  filters: any[];
  results: any[];
  last_updated: string;
}

const BOHFilter: React.FC = () => {
  const [data, setData] = useState<BOHFilterData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter state
  const [filters, setFilters] = useState({
    priceRange: [0, 10000],
    volumeMin: 100000,
    marketCapRange: [100, 100000],
    sector: '',
    exchange: 'NSE',
    rsiRange: [30, 70],
    enableRSI: false,
    enableVolume: true,
    enableMarketCap: true,
  });

  useEffect(() => {
    fetchBOHData();
  }, []);

  const fetchBOHData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getBOHFilter();
      setData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch BOH filter data');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const runFilter = () => {
    // TODO: Implement filter execution
    console.log('Running BOH filter with:', filters);
  };

  const saveFilter = () => {
    // TODO: Implement filter saving
    console.log('Saving BOH filter:', filters);
  };

  const resetFilters = () => {
    setFilters({
      priceRange: [0, 10000],
      volumeMin: 100000,
      marketCapRange: [100, 100000],
      sector: '',
      exchange: 'NSE',
      rsiRange: [30, 70],
      enableRSI: false,
      enableVolume: true,
      enableMarketCap: true,
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          BOH Filter
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchBOHData}
            sx={{ mr: 2 }}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            onClick={resetFilters}
            sx={{ mr: 2 }}
          >
            Reset
          </Button>
          <Button
            variant="outlined"
            startIcon={<SaveIcon />}
            onClick={saveFilter}
            sx={{ mr: 2 }}
          >
            Save Filter
          </Button>
          <Button
            variant="contained"
            startIcon={<RunIcon />}
            onClick={runFilter}
          >
            Run Filter
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Filter Configuration */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <FilterIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Filter Configuration
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                {/* Exchange Selection */}
                <FormControl fullWidth sx={{ mb: 3 }}>
                  <InputLabel>Exchange</InputLabel>
                  <Select
                    value={filters.exchange}
                    label="Exchange"
                    onChange={(e) => handleFilterChange('exchange', e.target.value)}
                  >
                    <MenuItem value="NSE">NSE</MenuItem>
                    <MenuItem value="BSE">BSE</MenuItem>
                    <MenuItem value="ALL">All Exchanges</MenuItem>
                  </Select>
                </FormControl>

                {/* Sector Selection */}
                <FormControl fullWidth sx={{ mb: 3 }}>
                  <InputLabel>Sector</InputLabel>
                  <Select
                    value={filters.sector}
                    label="Sector"
                    onChange={(e) => handleFilterChange('sector', e.target.value)}
                  >
                    <MenuItem value="">All Sectors</MenuItem>
                    <MenuItem value="Technology">Technology</MenuItem>
                    <MenuItem value="Banking">Banking</MenuItem>
                    <MenuItem value="Pharmaceutical">Pharmaceutical</MenuItem>
                    <MenuItem value="Automobile">Automobile</MenuItem>
                    <MenuItem value="Energy">Energy</MenuItem>
                  </Select>
                </FormControl>

                <Divider sx={{ my: 2 }} />

                {/* Price Range */}
                <Typography gutterBottom>
                  Price Range: ₹{filters.priceRange[0]} - ₹{filters.priceRange[1]}
                </Typography>
                <Slider
                  value={filters.priceRange}
                  onChange={(e, value) => handleFilterChange('priceRange', value)}
                  valueLabelDisplay="auto"
                  min={0}
                  max={10000}
                  sx={{ mb: 3 }}
                />

                {/* Market Cap Filter */}
                <FormControlLabel
                  control={
                    <Switch
                      checked={filters.enableMarketCap}
                      onChange={(e) => handleFilterChange('enableMarketCap', e.target.checked)}
                    />
                  }
                  label="Enable Market Cap Filter"
                />
                
                {filters.enableMarketCap && (
                  <Box sx={{ mt: 2, mb: 3 }}>
                    <Typography gutterBottom>
                      Market Cap Range: ₹{filters.marketCapRange[0]}Cr - ₹{filters.marketCapRange[1]}Cr
                    </Typography>
                    <Slider
                      value={filters.marketCapRange}
                      onChange={(e, value) => handleFilterChange('marketCapRange', value)}
                      valueLabelDisplay="auto"
                      min={100}
                      max={100000}
                    />
                  </Box>
                )}

                {/* Volume Filter */}
                <FormControlLabel
                  control={
                    <Switch
                      checked={filters.enableVolume}
                      onChange={(e) => handleFilterChange('enableVolume', e.target.checked)}
                    />
                  }
                  label="Enable Volume Filter"
                />
                
                {filters.enableVolume && (
                  <TextField
                    fullWidth
                    label="Minimum Volume"
                    type="number"
                    value={filters.volumeMin}
                    onChange={(e) => handleFilterChange('volumeMin', parseInt(e.target.value))}
                    sx={{ mt: 2, mb: 3 }}
                  />
                )}

                {/* RSI Filter */}
                <FormControlLabel
                  control={
                    <Switch
                      checked={filters.enableRSI}
                      onChange={(e) => handleFilterChange('enableRSI', e.target.checked)}
                    />
                  }
                  label="Enable RSI Filter"
                />
                
                {filters.enableRSI && (
                  <Box sx={{ mt: 2 }}>
                    <Typography gutterBottom>
                      RSI Range: {filters.rsiRange[0]} - {filters.rsiRange[1]}
                    </Typography>
                    <Slider
                      value={filters.rsiRange}
                      onChange={(e, value) => handleFilterChange('rsiRange', value)}
                      valueLabelDisplay="auto"
                      min={0}
                      max={100}
                    />
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Filter Results */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Filter Results
              </Typography>
              
              {data?.message && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  {data.message}
                </Alert>
              )}

              <Box sx={{ textAlign: 'center', py: 8 }}>
                <Typography variant="h6" color="textSecondary" gutterBottom>
                  BOH Filter Logic Implementation Pending
                </Typography>
                <Typography color="textSecondary" sx={{ mb: 3 }}>
                  The business logic for BOH (Breakout of High) filtering will be implemented based on your specific requirements.
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Current filter configuration:
                </Typography>
                <Box sx={{ mt: 2, textAlign: 'left', maxWidth: 400, mx: 'auto' }}>
                  <Typography variant="body2">• Exchange: {filters.exchange}</Typography>
                  <Typography variant="body2">• Price Range: ₹{filters.priceRange[0]} - ₹{filters.priceRange[1]}</Typography>
                  {filters.enableMarketCap && (
                    <Typography variant="body2">• Market Cap: ₹{filters.marketCapRange[0]}Cr - ₹{filters.marketCapRange[1]}Cr</Typography>
                  )}
                  {filters.enableVolume && (
                    <Typography variant="body2">• Min Volume: {filters.volumeMin.toLocaleString()}</Typography>
                  )}
                  {filters.enableRSI && (
                    <Typography variant="body2">• RSI Range: {filters.rsiRange[0]} - {filters.rsiRange[1]}</Typography>
                  )}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BOHFilter;
