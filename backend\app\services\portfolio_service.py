from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import Optional, List
from datetime import datetime
from app.db.models import Portfolio, Holding, Transaction, Stock
from app.schemas.portfolio import (
    PortfolioCreate, PortfolioUpdate, PortfolioWithHoldings,
    HoldingCreate, HoldingUpdate,
    TransactionCreate,
    PortfolioSummary
)
from app.services.stock_service import StockService


class PortfolioService:
    def __init__(self, db: Session):
        self.db = db
        self.stock_service = StockService(db)

    async def get_portfolios(self, skip: int = 0, limit: int = 100) -> List[Portfolio]:
        """Get all portfolios with pagination"""
        return self.db.query(Portfolio).filter(Portfolio.is_active == True).offset(skip).limit(limit).all()

    async def create_portfolio(self, portfolio_data: PortfolioCreate, user_id: int) -> Portfolio:
        """Create a new portfolio"""
        db_portfolio = Portfolio(
            **portfolio_data.dict(),
            user_id=user_id,
            current_value=portfolio_data.initial_capital,
            cash_balance=portfolio_data.initial_capital,
            total_invested=0.0,
            total_returns=0.0,
            returns_percentage=0.0
        )
        self.db.add(db_portfolio)
        self.db.commit()
        self.db.refresh(db_portfolio)
        return db_portfolio

    async def get_portfolio_with_holdings(self, portfolio_id: int) -> Optional[PortfolioWithHoldings]:
        """Get portfolio with holdings and recent transactions"""
        portfolio = self.db.query(Portfolio).filter(Portfolio.id == portfolio_id).first()
        if not portfolio:
            return None
        
        holdings = self.db.query(Holding).filter(Holding.portfolio_id == portfolio_id).all()
        recent_transactions = (
            self.db.query(Transaction)
            .filter(Transaction.portfolio_id == portfolio_id)
            .order_by(Transaction.created_at.desc())
            .limit(10)
            .all()
        )
        
        return PortfolioWithHoldings(
            **portfolio.__dict__,
            holdings=holdings,
            recent_transactions=recent_transactions
        )

    async def update_portfolio(self, portfolio_id: int, portfolio_update: PortfolioUpdate) -> Optional[Portfolio]:
        """Update portfolio"""
        db_portfolio = self.db.query(Portfolio).filter(Portfolio.id == portfolio_id).first()
        if not db_portfolio:
            return None
        
        update_data = portfolio_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_portfolio, field, value)
        
        self.db.commit()
        self.db.refresh(db_portfolio)
        return db_portfolio

    async def delete_portfolio(self, portfolio_id: int) -> bool:
        """Delete portfolio (soft delete)"""
        db_portfolio = self.db.query(Portfolio).filter(Portfolio.id == portfolio_id).first()
        if not db_portfolio:
            return False
        
        db_portfolio.is_active = False
        self.db.commit()
        return True

    async def get_portfolio_summary(self, portfolio_id: int) -> Optional[PortfolioSummary]:
        """Get portfolio summary"""
        portfolio = self.db.query(Portfolio).filter(Portfolio.id == portfolio_id).first()
        if not portfolio:
            return None
        
        holdings = self.db.query(Holding).filter(Holding.portfolio_id == portfolio_id).all()
        
        # Calculate summary metrics
        total_value = portfolio.cash_balance + sum(holding.market_value for holding in holdings)
        total_invested = portfolio.total_invested
        total_returns = total_value - portfolio.initial_capital
        returns_percentage = (total_returns / portfolio.initial_capital) * 100 if portfolio.initial_capital > 0 else 0
        
        # Get top holdings (by market value)
        top_holdings = sorted(holdings, key=lambda x: x.market_value, reverse=True)[:5]
        
        return PortfolioSummary(
            total_value=total_value,
            total_invested=total_invested,
            total_returns=total_returns,
            returns_percentage=returns_percentage,
            cash_balance=portfolio.cash_balance,
            holdings_count=len(holdings),
            top_holdings=top_holdings
        )

    async def get_holdings(self, portfolio_id: int) -> List[Holding]:
        """Get portfolio holdings"""
        return self.db.query(Holding).filter(Holding.portfolio_id == portfolio_id).all()

    async def add_holding(self, holding_data: HoldingCreate) -> Holding:
        """Add holding to portfolio"""
        # Check if holding already exists
        existing_holding = self.db.query(Holding).filter(
            and_(
                Holding.portfolio_id == holding_data.portfolio_id,
                Holding.stock_id == holding_data.stock_id
            )
        ).first()
        
        if existing_holding:
            # Update existing holding (average price calculation)
            total_quantity = existing_holding.quantity + holding_data.quantity
            total_cost = (existing_holding.quantity * existing_holding.average_price) + \
                        (holding_data.quantity * holding_data.average_price)
            new_average_price = total_cost / total_quantity
            
            existing_holding.quantity = total_quantity
            existing_holding.average_price = new_average_price
            self.db.commit()
            self.db.refresh(existing_holding)
            return existing_holding
        else:
            # Create new holding
            db_holding = Holding(
                **holding_data.dict(),
                current_price=holding_data.average_price,  # Will be updated with real price
                market_value=holding_data.quantity * holding_data.average_price,
                unrealized_pnl=0.0,
                unrealized_pnl_percentage=0.0
            )
            self.db.add(db_holding)
            self.db.commit()
            self.db.refresh(db_holding)
            return db_holding

    async def update_holding(self, holding_id: int, holding_update: HoldingUpdate) -> Optional[Holding]:
        """Update holding"""
        db_holding = self.db.query(Holding).filter(Holding.id == holding_id).first()
        if not db_holding:
            return None
        
        update_data = holding_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_holding, field, value)
        
        # Recalculate market value and P&L
        if hasattr(holding_update, 'current_price') and holding_update.current_price:
            db_holding.market_value = db_holding.quantity * db_holding.current_price
            db_holding.unrealized_pnl = db_holding.market_value - (db_holding.quantity * db_holding.average_price)
            db_holding.unrealized_pnl_percentage = (db_holding.unrealized_pnl / (db_holding.quantity * db_holding.average_price)) * 100
        
        self.db.commit()
        self.db.refresh(db_holding)
        return db_holding

    async def remove_holding(self, holding_id: int) -> bool:
        """Remove holding from portfolio"""
        db_holding = self.db.query(Holding).filter(Holding.id == holding_id).first()
        if not db_holding:
            return False
        
        self.db.delete(db_holding)
        self.db.commit()
        return True

    async def get_transactions(self, portfolio_id: int, skip: int = 0, limit: int = 100) -> List[Transaction]:
        """Get portfolio transactions"""
        return (
            self.db.query(Transaction)
            .filter(Transaction.portfolio_id == portfolio_id)
            .order_by(Transaction.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    async def add_transaction(self, transaction_data: TransactionCreate) -> Transaction:
        """Add transaction to portfolio"""
        db_transaction = Transaction(**transaction_data.dict())
        self.db.add(db_transaction)
        self.db.commit()
        self.db.refresh(db_transaction)
        
        # Update portfolio and holdings based on transaction
        await self._process_transaction(db_transaction)
        
        return db_transaction

    async def refresh_portfolio_values(self, portfolio_id: int) -> Optional[Portfolio]:
        """Refresh portfolio values with current market prices"""
        portfolio = self.db.query(Portfolio).filter(Portfolio.id == portfolio_id).first()
        if not portfolio:
            return None
        
        holdings = self.db.query(Holding).filter(Holding.portfolio_id == portfolio_id).all()
        
        total_market_value = 0
        for holding in holdings:
            # Get current price from stock service
            stock = self.db.query(Stock).filter(Stock.id == holding.stock_id).first()
            if stock:
                current_price_data = await self.stock_service.get_current_price(stock.symbol)
                if 'current_price' in current_price_data:
                    holding.current_price = current_price_data['current_price']
                    holding.market_value = holding.quantity * holding.current_price
                    holding.unrealized_pnl = holding.market_value - (holding.quantity * holding.average_price)
                    holding.unrealized_pnl_percentage = (holding.unrealized_pnl / (holding.quantity * holding.average_price)) * 100
                    total_market_value += holding.market_value
        
        # Update portfolio values
        portfolio.current_value = portfolio.cash_balance + total_market_value
        portfolio.total_returns = portfolio.current_value - portfolio.initial_capital
        portfolio.returns_percentage = (portfolio.total_returns / portfolio.initial_capital) * 100 if portfolio.initial_capital > 0 else 0
        
        self.db.commit()
        self.db.refresh(portfolio)
        return portfolio

    async def _process_transaction(self, transaction: Transaction):
        """Process transaction and update portfolio/holdings"""
        portfolio = self.db.query(Portfolio).filter(Portfolio.id == transaction.portfolio_id).first()
        
        if transaction.transaction_type == "BUY":
            # Decrease cash balance
            portfolio.cash_balance -= transaction.total_amount
            portfolio.total_invested += transaction.total_amount
            
            # Update or create holding
            holding_data = HoldingCreate(
                portfolio_id=transaction.portfolio_id,
                stock_id=transaction.stock_id,
                quantity=transaction.quantity,
                average_price=transaction.price
            )
            await self.add_holding(holding_data)
            
        elif transaction.transaction_type == "SELL":
            # Increase cash balance
            portfolio.cash_balance += transaction.total_amount
            
            # Update holding
            holding = self.db.query(Holding).filter(
                and_(
                    Holding.portfolio_id == transaction.portfolio_id,
                    Holding.stock_id == transaction.stock_id
                )
            ).first()
            
            if holding:
                holding.quantity -= transaction.quantity
                if holding.quantity <= 0:
                    self.db.delete(holding)
        
        self.db.commit()
