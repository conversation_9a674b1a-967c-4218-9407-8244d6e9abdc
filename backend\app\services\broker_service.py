import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../smartapi-python-main/smartapi-python-main'))

from SmartApi import SmartConnect
import pyotp
from logzero import logger
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.db.models import BrokerConnection, GTTOrder, Transaction
from app.core.config import settings
import json


class BrokerService:
    def __init__(self, db: Session):
        self.db = db
        self.smart_api = None
        self.auth_token = None
        self.refresh_token = None
        self.feed_token = None
        self.connection = None

    async def connect_broker(self, api_key: str, client_code: str, pin: str, totp_secret: str) -> Dict[str, Any]:
        """Connect to Angel Broking SmartAPI"""
        try:
            # Initialize SmartConnect
            self.smart_api = SmartConnect(api_key)
            
            # Generate TOTP
            totp = pyotp.TOTP(totp_secret).now()
            
            # Generate session
            data = self.smart_api.generateSession(client_code, pin, totp)
            
            if data['status'] == False:
                return {
                    "success": False,
                    "error": data.get('message', 'Authentication failed'),
                    "data": data
                }
            
            # Extract tokens
            self.auth_token = data['data']['jwtToken']
            self.refresh_token = data['data']['refreshToken']
            self.feed_token = self.smart_api.getfeedToken()
            
            # Get user profile
            profile = self.smart_api.getProfile(self.refresh_token)
            
            # Update or create broker connection in database
            connection = self.db.query(BrokerConnection).first()
            if connection:
                connection.api_key = api_key
                connection.client_code = client_code
                connection.is_connected = True
                connection.last_connected = datetime.now()
                connection.connection_status = "Connected"
                connection.auth_token = self.auth_token
                connection.refresh_token = self.refresh_token
                connection.feed_token = self.feed_token
            else:
                connection = BrokerConnection(
                    broker_name="ANGEL_BROKING",
                    api_key=api_key,
                    client_code=client_code,
                    is_connected=True,
                    last_connected=datetime.now(),
                    connection_status="Connected",
                    auth_token=self.auth_token,
                    refresh_token=self.refresh_token,
                    feed_token=self.feed_token
                )
                self.db.add(connection)
            
            self.db.commit()
            self.connection = connection
            
            return {
                "success": True,
                "message": "Successfully connected to Angel Broking",
                "profile": profile['data'] if profile['status'] else None,
                "connection_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Broker connection failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def disconnect_broker(self) -> Dict[str, Any]:
        """Disconnect from broker"""
        try:
            if self.smart_api and self.connection:
                # Terminate session
                self.smart_api.terminateSession(self.connection.client_code)
                
                # Update database
                self.connection.is_connected = False
                self.connection.connection_status = "Disconnected"
                self.connection.auth_token = None
                self.connection.refresh_token = None
                self.connection.feed_token = None
                self.db.commit()
                
                # Clear local variables
                self.smart_api = None
                self.auth_token = None
                self.refresh_token = None
                self.feed_token = None
                
                return {
                    "success": True,
                    "message": "Successfully disconnected from broker"
                }
            else:
                return {
                    "success": False,
                    "error": "No active connection found"
                }
                
        except Exception as e:
            logger.error(f"Broker disconnection failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_connection_status(self) -> Dict[str, Any]:
        """Get current broker connection status"""
        connection = self.db.query(BrokerConnection).first()
        
        if connection:
            return {
                "is_connected": connection.is_connected,
                "broker_name": connection.broker_name,
                "client_code": connection.client_code,
                "last_connected": connection.last_connected.isoformat() if connection.last_connected else None,
                "connection_status": connection.connection_status
            }
        else:
            return {
                "is_connected": False,
                "broker_name": None,
                "client_code": None,
                "last_connected": None,
                "connection_status": "Not configured"
            }

    async def place_order(self, order_params: Dict[str, Any]) -> Dict[str, Any]:
        """Place order through broker"""
        try:
            if not self.smart_api or not self.connection or not self.connection.is_connected:
                return {
                    "success": False,
                    "error": "Broker not connected"
                }
            
            # Place order
            order_id = self.smart_api.placeOrder(order_params)
            
            if order_id:
                # Create transaction record
                transaction = Transaction(
                    portfolio_id=order_params.get('portfolio_id'),
                    stock_id=order_params.get('stock_id'),
                    transaction_type=order_params['transactiontype'],
                    quantity=int(order_params['quantity']),
                    price=float(order_params['price']),
                    total_amount=float(order_params['price']) * int(order_params['quantity']),
                    transaction_date=datetime.now(),
                    order_id=str(order_id),
                    status="PENDING"
                )
                self.db.add(transaction)
                self.db.commit()
                
                return {
                    "success": True,
                    "order_id": order_id,
                    "message": "Order placed successfully"
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to place order"
                }
                
        except Exception as e:
            logger.error(f"Order placement failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_order_history(self) -> Dict[str, Any]:
        """Get order history from broker"""
        try:
            if not self.smart_api or not self.connection or not self.connection.is_connected:
                return {
                    "success": False,
                    "error": "Broker not connected"
                }
            
            # Get order book
            orders = self.smart_api.orderBook()
            
            return {
                "success": True,
                "orders": orders['data'] if orders['status'] else [],
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get order history: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_positions(self) -> Dict[str, Any]:
        """Get current positions from broker"""
        try:
            if not self.smart_api or not self.connection or not self.connection.is_connected:
                return {
                    "success": False,
                    "error": "Broker not connected"
                }
            
            # Get positions
            positions = self.smart_api.position()
            
            return {
                "success": True,
                "positions": positions['data'] if positions['status'] else [],
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get positions: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_holdings(self) -> Dict[str, Any]:
        """Get holdings from broker"""
        try:
            if not self.smart_api or not self.connection or not self.connection.is_connected:
                return {
                    "success": False,
                    "error": "Broker not connected"
                }
            
            # Get holdings
            holdings = self.smart_api.holding()
            
            return {
                "success": True,
                "holdings": holdings['data'] if holdings['status'] else [],
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get holdings: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def create_gtt_order(self, gtt_params: Dict[str, Any]) -> Dict[str, Any]:
        """Create GTT (Good Till Triggered) order"""
        try:
            if not self.smart_api or not self.connection or not self.connection.is_connected:
                return {
                    "success": False,
                    "error": "Broker not connected"
                }
            
            # Create GTT rule
            rule_id = self.smart_api.gttCreateRule(gtt_params)
            
            if rule_id:
                # Create GTT order record in database
                gtt_order = GTTOrder(
                    stock_id=gtt_params.get('stock_id'),
                    order_type=gtt_params['transactiontype'],
                    trigger_price=float(gtt_params['triggerprice']),
                    quantity=int(gtt_params['qty']),
                    price=float(gtt_params['price']),
                    status="ACTIVE",
                    broker_order_id=str(rule_id),
                    expires_at=datetime.now() + timedelta(days=gtt_params.get('timeperiod', 365))
                )
                self.db.add(gtt_order)
                self.db.commit()
                
                return {
                    "success": True,
                    "rule_id": rule_id,
                    "message": "GTT order created successfully"
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to create GTT order"
                }
                
        except Exception as e:
            logger.error(f"GTT order creation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_gtt_orders(self) -> Dict[str, Any]:
        """Get GTT orders from broker"""
        try:
            if not self.smart_api or not self.connection or not self.connection.is_connected:
                return {
                    "success": False,
                    "error": "Broker not connected"
                }
            
            # Get GTT rules
            status = ["FORALL"]
            page = 1
            count = 100
            gtt_orders = self.smart_api.gttLists(status, page, count)
            
            return {
                "success": True,
                "gtt_orders": gtt_orders['data'] if gtt_orders['status'] else [],
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get GTT orders: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_historical_data(self, symbol_token: str, exchange: str, interval: str, from_date: str, to_date: str) -> Dict[str, Any]:
        """Get historical data from broker"""
        try:
            if not self.smart_api or not self.connection or not self.connection.is_connected:
                return {
                    "success": False,
                    "error": "Broker not connected"
                }
            
            historic_param = {
                "exchange": exchange,
                "symboltoken": symbol_token,
                "interval": interval,
                "fromdate": from_date,
                "todate": to_date
            }
            
            historical_data = self.smart_api.getCandleData(historic_param)
            
            return {
                "success": True,
                "data": historical_data['data'] if historical_data['status'] else [],
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get historical data: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_account_info(self) -> Dict[str, Any]:
        """Get account information"""
        try:
            if not self.smart_api or not self.connection or not self.connection.is_connected:
                return {
                    "success": False,
                    "error": "Broker not connected"
                }
            
            # Get user profile
            profile = self.smart_api.getProfile(self.refresh_token)
            
            # Get RMS (Risk Management System) limits
            rms = self.smart_api.rmsLimit()
            
            return {
                "success": True,
                "profile": profile['data'] if profile['status'] else {},
                "rms_limits": rms['data'] if rms['status'] else {},
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get account info: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def refresh_token(self) -> Dict[str, Any]:
        """Refresh authentication token"""
        try:
            if not self.smart_api or not self.refresh_token:
                return {
                    "success": False,
                    "error": "No refresh token available"
                }
            
            # Generate new token
            new_token = self.smart_api.generateToken(self.refresh_token)
            
            if new_token:
                self.auth_token = new_token
                
                # Update database
                if self.connection:
                    self.connection.auth_token = self.auth_token
                    self.db.commit()
                
                return {
                    "success": True,
                    "message": "Token refreshed successfully"
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to refresh token"
                }
                
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
