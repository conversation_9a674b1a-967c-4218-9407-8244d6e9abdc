import yfinance as yf
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.db.models import Stock, StockPrice
from app.core.config import settings
import asyncio
import aiohttp
import json
from functools import lru_cache


class YahooFinanceService:
    def __init__(self, db: Session):
        self.db = db
        self.timeout = settings.yahoo_finance_timeout

    @lru_cache(maxsize=100)
    def _get_ticker(self, symbol: str) -> yf.Ticker:
        """Get cached ticker object"""
        return yf.Ticker(symbol)

    async def get_stock_info(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive stock information"""
        try:
            ticker = self._get_ticker(symbol)
            info = ticker.info
            
            return {
                "symbol": symbol,
                "name": info.get('longName', ''),
                "sector": info.get('sector', ''),
                "industry": info.get('industry', ''),
                "market_cap": info.get('marketCap', 0),
                "current_price": info.get('currentPrice', 0),
                "previous_close": info.get('previousClose', 0),
                "open": info.get('open', 0),
                "day_high": info.get('dayHigh', 0),
                "day_low": info.get('dayLow', 0),
                "volume": info.get('volume', 0),
                "average_volume": info.get('averageVolume', 0),
                "pe_ratio": info.get('trailingPE', 0),
                "forward_pe": info.get('forwardPE', 0),
                "dividend_yield": info.get('dividendYield', 0),
                "beta": info.get('beta', 0),
                "52_week_high": info.get('fiftyTwoWeekHigh', 0),
                "52_week_low": info.get('fiftyTwoWeekLow', 0),
                "exchange": info.get('exchange', ''),
                "currency": info.get('currency', 'USD'),
                "last_updated": datetime.now().isoformat()
            }
        except Exception as e:
            return {"error": f"Failed to fetch stock info: {str(e)}"}

    async def get_historical_data(
        self,
        symbol: str,
        period: str = "1y",
        interval: str = "1d",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get historical price data"""
        try:
            ticker = self._get_ticker(symbol)
            
            if start_date and end_date:
                hist = ticker.history(start=start_date, end=end_date, interval=interval)
            else:
                hist = ticker.history(period=period, interval=interval)
            
            if hist.empty:
                return {"error": "No historical data found"}
            
            # Convert to list of dictionaries
            data = []
            for date, row in hist.iterrows():
                data.append({
                    "date": date.isoformat(),
                    "open": float(row['Open']),
                    "high": float(row['High']),
                    "low": float(row['Low']),
                    "close": float(row['Close']),
                    "volume": int(row['Volume']),
                    "adjusted_close": float(row['Close'])
                })
            
            return {
                "symbol": symbol,
                "period": period,
                "interval": interval,
                "data": data,
                "count": len(data)
            }
        except Exception as e:
            return {"error": f"Failed to fetch historical data: {str(e)}"}

    async def get_multiple_quotes(self, symbols: List[str]) -> Dict[str, Any]:
        """Get current quotes for multiple symbols"""
        try:
            # Use yfinance download for multiple symbols
            data = yf.download(symbols, period="1d", interval="1m", group_by='ticker')
            
            quotes = {}
            for symbol in symbols:
                try:
                    if len(symbols) == 1:
                        symbol_data = data
                    else:
                        symbol_data = data[symbol]
                    
                    if not symbol_data.empty:
                        latest = symbol_data.iloc[-1]
                        quotes[symbol] = {
                            "current_price": float(latest['Close']),
                            "open": float(latest['Open']),
                            "high": float(latest['High']),
                            "low": float(latest['Low']),
                            "volume": int(latest['Volume']),
                            "last_updated": datetime.now().isoformat()
                        }
                    else:
                        quotes[symbol] = {"error": "No data available"}
                except Exception as e:
                    quotes[symbol] = {"error": str(e)}
            
            return quotes
        except Exception as e:
            return {"error": f"Failed to fetch multiple quotes: {str(e)}"}

    async def search_stocks(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for stocks by symbol or name"""
        try:
            # This is a simplified search - in production, you might want to use
            # a more comprehensive stock database or API
            results = []
            
            # Try to get info for the query as a symbol
            try:
                ticker = yf.Ticker(query.upper())
                info = ticker.info
                if info and 'longName' in info:
                    results.append({
                        "symbol": query.upper(),
                        "name": info.get('longName', ''),
                        "exchange": info.get('exchange', ''),
                        "type": "stock"
                    })
            except:
                pass
            
            return results[:limit]
        except Exception as e:
            return []

    async def get_market_indices(self) -> Dict[str, Any]:
        """Get major market indices data"""
        indices = {
            "^NSEI": "NIFTY 50",
            "^BSESN": "BSE SENSEX",
            "^NSEBANK": "NIFTY BANK",
            "^GSPC": "S&P 500",
            "^DJI": "Dow Jones",
            "^IXIC": "NASDAQ"
        }
        
        results = {}
        for symbol, name in indices.items():
            try:
                ticker = self._get_ticker(symbol)
                hist = ticker.history(period="2d", interval="1d")
                
                if not hist.empty:
                    current = hist.iloc[-1]
                    previous = hist.iloc[-2] if len(hist) > 1 else current
                    
                    change = current['Close'] - previous['Close']
                    change_percentage = (change / previous['Close']) * 100
                    
                    results[symbol] = {
                        "name": name,
                        "value": float(current['Close']),
                        "change": float(change),
                        "change_percentage": float(change_percentage),
                        "volume": int(current['Volume']),
                        "last_updated": datetime.now().isoformat()
                    }
            except Exception as e:
                results[symbol] = {
                    "name": name,
                    "error": str(e)
                }
        
        return results

    async def get_sector_performance(self) -> Dict[str, Any]:
        """Get sector-wise performance data"""
        # Major sector ETFs
        sector_etfs = {
            "XLK": "Technology",
            "XLF": "Financial",
            "XLV": "Healthcare",
            "XLE": "Energy",
            "XLI": "Industrial",
            "XLY": "Consumer Discretionary",
            "XLP": "Consumer Staples",
            "XLU": "Utilities",
            "XLB": "Materials",
            "XLRE": "Real Estate"
        }
        
        results = {}
        for symbol, sector in sector_etfs.items():
            try:
                ticker = self._get_ticker(symbol)
                hist = ticker.history(period="2d", interval="1d")
                
                if not hist.empty:
                    current = hist.iloc[-1]
                    previous = hist.iloc[-2] if len(hist) > 1 else current
                    
                    change = current['Close'] - previous['Close']
                    change_percentage = (change / previous['Close']) * 100
                    
                    results[sector] = {
                        "symbol": symbol,
                        "value": float(current['Close']),
                        "change": float(change),
                        "change_percentage": float(change_percentage)
                    }
            except Exception as e:
                results[sector] = {"error": str(e)}
        
        return results

    async def update_stock_prices(self, symbols: List[str]) -> Dict[str, Any]:
        """Update stock prices in database"""
        updated_count = 0
        errors = []
        
        for symbol in symbols:
            try:
                # Get stock from database
                stock = self.db.query(Stock).filter(Stock.symbol == symbol).first()
                if not stock:
                    continue
                
                # Get latest price data
                ticker = self._get_ticker(symbol)
                hist = ticker.history(period="5d", interval="1d")
                
                for date, row in hist.iterrows():
                    # Check if price data already exists
                    existing_price = self.db.query(StockPrice).filter(
                        StockPrice.stock_id == stock.id,
                        StockPrice.date == date.date()
                    ).first()
                    
                    if not existing_price:
                        price_data = StockPrice(
                            stock_id=stock.id,
                            date=date,
                            open_price=float(row['Open']),
                            high_price=float(row['High']),
                            low_price=float(row['Low']),
                            close_price=float(row['Close']),
                            volume=int(row['Volume']),
                            adjusted_close=float(row['Close'])
                        )
                        self.db.add(price_data)
                        updated_count += 1
                
            except Exception as e:
                errors.append(f"{symbol}: {str(e)}")
        
        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            errors.append(f"Database commit error: {str(e)}")
        
        return {
            "updated_count": updated_count,
            "errors": errors,
            "last_updated": datetime.now().isoformat()
        }

    async def get_technical_indicators(self, symbol: str, period: str = "3mo") -> Dict[str, Any]:
        """Calculate basic technical indicators"""
        try:
            ticker = self._get_ticker(symbol)
            hist = ticker.history(period=period, interval="1d")
            
            if hist.empty:
                return {"error": "No data available for technical analysis"}
            
            # Calculate moving averages
            hist['MA_20'] = hist['Close'].rolling(window=20).mean()
            hist['MA_50'] = hist['Close'].rolling(window=50).mean()
            hist['MA_200'] = hist['Close'].rolling(window=200).mean()
            
            # Calculate RSI
            delta = hist['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            hist['RSI'] = 100 - (100 / (1 + rs))
            
            # Get latest values
            latest = hist.iloc[-1]
            
            return {
                "symbol": symbol,
                "current_price": float(latest['Close']),
                "ma_20": float(latest['MA_20']) if not pd.isna(latest['MA_20']) else None,
                "ma_50": float(latest['MA_50']) if not pd.isna(latest['MA_50']) else None,
                "ma_200": float(latest['MA_200']) if not pd.isna(latest['MA_200']) else None,
                "rsi": float(latest['RSI']) if not pd.isna(latest['RSI']) else None,
                "volume": int(latest['Volume']),
                "last_updated": datetime.now().isoformat()
            }
        except Exception as e:
            return {"error": f"Failed to calculate technical indicators: {str(e)}"}
