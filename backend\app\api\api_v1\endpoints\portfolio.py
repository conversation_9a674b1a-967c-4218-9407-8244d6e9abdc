from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List
from app.db.database import get_db
from app.schemas.portfolio import (
    Portfolio, PortfolioCreate, PortfolioUpdate, PortfolioWithHoldings,
    Holding, HoldingCreate, HoldingUpdate,
    Transaction, TransactionCreate,
    PortfolioSummary
)
from app.services.portfolio_service import PortfolioService

router = APIRouter()


@router.get("/", response_model=List[Portfolio])
async def get_portfolios(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """Get all portfolios"""
    portfolio_service = PortfolioService(db)
    return await portfolio_service.get_portfolios(skip=skip, limit=limit)


@router.post("/", response_model=Portfolio)
async def create_portfolio(portfolio: PortfolioCreate, db: Session = Depends(get_db)):
    """Create a new portfolio"""
    portfolio_service = PortfolioService(db)
    return await portfolio_service.create_portfolio(portfolio, user_id=1)  # TODO: Get from auth


@router.get("/{portfolio_id}", response_model=PortfolioWithHoldings)
async def get_portfolio(portfolio_id: int, db: Session = Depends(get_db)):
    """Get portfolio details with holdings"""
    portfolio_service = PortfolioService(db)
    portfolio = await portfolio_service.get_portfolio_with_holdings(portfolio_id)
    if not portfolio:
        raise HTTPException(status_code=404, detail="Portfolio not found")
    return portfolio


@router.put("/{portfolio_id}", response_model=Portfolio)
async def update_portfolio(
    portfolio_id: int,
    portfolio_update: PortfolioUpdate,
    db: Session = Depends(get_db)
):
    """Update portfolio"""
    portfolio_service = PortfolioService(db)
    portfolio = await portfolio_service.update_portfolio(portfolio_id, portfolio_update)
    if not portfolio:
        raise HTTPException(status_code=404, detail="Portfolio not found")
    return portfolio


@router.delete("/{portfolio_id}")
async def delete_portfolio(portfolio_id: int, db: Session = Depends(get_db)):
    """Delete portfolio"""
    portfolio_service = PortfolioService(db)
    success = await portfolio_service.delete_portfolio(portfolio_id)
    if not success:
        raise HTTPException(status_code=404, detail="Portfolio not found")
    return {"message": "Portfolio deleted successfully"}


@router.get("/{portfolio_id}/summary", response_model=PortfolioSummary)
async def get_portfolio_summary(portfolio_id: int, db: Session = Depends(get_db)):
    """Get portfolio summary"""
    portfolio_service = PortfolioService(db)
    summary = await portfolio_service.get_portfolio_summary(portfolio_id)
    if not summary:
        raise HTTPException(status_code=404, detail="Portfolio not found")
    return summary


@router.get("/{portfolio_id}/holdings", response_model=List[Holding])
async def get_holdings(portfolio_id: int, db: Session = Depends(get_db)):
    """Get portfolio holdings"""
    portfolio_service = PortfolioService(db)
    return await portfolio_service.get_holdings(portfolio_id)


@router.post("/{portfolio_id}/holdings", response_model=Holding)
async def add_holding(
    portfolio_id: int,
    holding: HoldingCreate,
    db: Session = Depends(get_db)
):
    """Add holding to portfolio"""
    holding.portfolio_id = portfolio_id
    portfolio_service = PortfolioService(db)
    return await portfolio_service.add_holding(holding)


@router.put("/{portfolio_id}/holdings/{holding_id}", response_model=Holding)
async def update_holding(
    portfolio_id: int,
    holding_id: int,
    holding_update: HoldingUpdate,
    db: Session = Depends(get_db)
):
    """Update holding"""
    portfolio_service = PortfolioService(db)
    holding = await portfolio_service.update_holding(holding_id, holding_update)
    if not holding:
        raise HTTPException(status_code=404, detail="Holding not found")
    return holding


@router.delete("/{portfolio_id}/holdings/{holding_id}")
async def remove_holding(
    portfolio_id: int,
    holding_id: int,
    db: Session = Depends(get_db)
):
    """Remove holding from portfolio"""
    portfolio_service = PortfolioService(db)
    success = await portfolio_service.remove_holding(holding_id)
    if not success:
        raise HTTPException(status_code=404, detail="Holding not found")
    return {"message": "Holding removed successfully"}


@router.get("/{portfolio_id}/transactions", response_model=List[Transaction])
async def get_transactions(
    portfolio_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """Get portfolio transactions"""
    portfolio_service = PortfolioService(db)
    return await portfolio_service.get_transactions(portfolio_id, skip=skip, limit=limit)


@router.post("/{portfolio_id}/transactions", response_model=Transaction)
async def add_transaction(
    portfolio_id: int,
    transaction: TransactionCreate,
    db: Session = Depends(get_db)
):
    """Add transaction to portfolio"""
    transaction.portfolio_id = portfolio_id
    portfolio_service = PortfolioService(db)
    return await portfolio_service.add_transaction(transaction)


@router.post("/{portfolio_id}/refresh")
async def refresh_portfolio(portfolio_id: int, db: Session = Depends(get_db)):
    """Refresh portfolio values with current market prices"""
    portfolio_service = PortfolioService(db)
    portfolio = await portfolio_service.refresh_portfolio_values(portfolio_id)
    if not portfolio:
        raise HTTPException(status_code=404, detail="Portfolio not found")
    return {"message": "Portfolio refreshed successfully", "portfolio": portfolio}
