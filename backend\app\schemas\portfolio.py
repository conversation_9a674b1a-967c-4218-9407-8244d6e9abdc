from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime


class PortfolioBase(BaseModel):
    name: str
    initial_capital: float


class PortfolioCreate(PortfolioBase):
    pass


class PortfolioUpdate(BaseModel):
    name: Optional[str] = None
    cash_balance: Optional[float] = None
    is_active: Optional[bool] = None


class Portfolio(PortfolioBase):
    id: int
    user_id: int
    current_value: float
    cash_balance: float
    total_invested: float
    total_returns: float
    returns_percentage: float
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class HoldingBase(BaseModel):
    quantity: int
    average_price: float


class HoldingCreate(HoldingBase):
    portfolio_id: int
    stock_id: int


class HoldingUpdate(BaseModel):
    quantity: Optional[int] = None
    average_price: Optional[float] = None
    current_price: Optional[float] = None


class Holding(HoldingBase):
    id: int
    portfolio_id: int
    stock_id: int
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_percentage: float
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class TransactionBase(BaseModel):
    transaction_type: str  # BUY, SELL
    quantity: int
    price: float
    total_amount: float
    fees: Optional[float] = 0.0
    transaction_date: datetime


class TransactionCreate(TransactionBase):
    portfolio_id: int
    stock_id: int
    order_id: Optional[str] = None


class Transaction(TransactionBase):
    id: int
    portfolio_id: int
    stock_id: int
    order_id: Optional[str] = None
    status: str
    created_at: datetime

    class Config:
        from_attributes = True


class PortfolioSummary(BaseModel):
    total_value: float
    total_invested: float
    total_returns: float
    returns_percentage: float
    cash_balance: float
    holdings_count: int
    top_holdings: List[Holding]


class PortfolioWithHoldings(Portfolio):
    holdings: List[Holding] = []
    recent_transactions: List[Transaction] = []
