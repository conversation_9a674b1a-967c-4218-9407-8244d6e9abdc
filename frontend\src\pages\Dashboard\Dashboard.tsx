import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Al<PERSON>,
  Chip,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AccountBalance,
  Notifications,
  Link as LinkIcon,
} from '@mui/icons-material';
import { dashboardApi } from '../../services/api';

interface DashboardData {
  total_portfolio_value: number;
  total_returns: number;
  returns_percentage: number;
  active_signals: number;
  active_gtt_orders: number;
  broker_connected: boolean;
  portfolios_count: number;
  last_updated: string;
}

const Dashboard: React.FC = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getOverview();
      setData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!data) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        No dashboard data available
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard Overview
      </Typography>
      
      <Grid container spacing={3}>
        {/* Portfolio Value Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Portfolio Value
                  </Typography>
                  <Typography variant="h5" component="div">
                    {formatCurrency(data.total_portfolio_value)}
                  </Typography>
                </Box>
                <AccountBalance color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Returns Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Returns
                  </Typography>
                  <Typography variant="h5" component="div" color={data.total_returns >= 0 ? 'success.main' : 'error.main'}>
                    {formatCurrency(data.total_returns)}
                  </Typography>
                  <Typography variant="body2" color={data.returns_percentage >= 0 ? 'success.main' : 'error.main'}>
                    {formatPercentage(data.returns_percentage)}
                  </Typography>
                </Box>
                {data.total_returns >= 0 ? (
                  <TrendingUp color="success" sx={{ fontSize: 40 }} />
                ) : (
                  <TrendingDown color="error" sx={{ fontSize: 40 }} />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Active Signals Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Active Signals
                  </Typography>
                  <Typography variant="h5" component="div">
                    {data.active_signals}
                  </Typography>
                </Box>
                <Notifications color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* GTT Orders Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Active GTT Orders
                  </Typography>
                  <Typography variant="h5" component="div">
                    {data.active_gtt_orders}
                  </Typography>
                </Box>
                <Notifications color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Status Cards */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Status
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography>Broker Connection</Typography>
                  <Chip
                    icon={<LinkIcon />}
                    label={data.broker_connected ? 'Connected' : 'Disconnected'}
                    color={data.broker_connected ? 'success' : 'error'}
                    variant="outlined"
                  />
                </Box>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Typography>Active Portfolios</Typography>
                  <Chip
                    label={data.portfolios_count}
                    color="primary"
                    variant="outlined"
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box display="flex" flexDirection="column" gap={1}>
                <Typography variant="body2" color="textSecondary">
                  • View Capital Management
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  • Check Weekly High Signals
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  • Run Backtesting
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  • Manage GTT Orders
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Last Updated */}
        <Grid item xs={12}>
          <Typography variant="body2" color="textSecondary" align="center">
            Last updated: {new Date(data.last_updated).toLocaleString()}
          </Typography>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
