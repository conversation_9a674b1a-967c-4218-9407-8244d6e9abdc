from fastapi import APIRouter
from app.api.api_v1.endpoints import (
    stocks,
    portfolio,
    strategies,
    backtesting,
    signals,
    broker,
    dashboard
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(stocks.router, prefix="/stocks", tags=["stocks"])
api_router.include_router(portfolio.router, prefix="/portfolio", tags=["portfolio"])
api_router.include_router(strategies.router, prefix="/strategies", tags=["strategies"])
api_router.include_router(backtesting.router, prefix="/backtesting", tags=["backtesting"])
api_router.include_router(signals.router, prefix="/signals", tags=["signals"])
api_router.include_router(broker.router, prefix="/broker", tags=["broker"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
