#!/usr/bin/env python3
"""
Auto-installer for New Darvas Box Trading Application
This script does EVERYTHING automatically with minimal user input
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def run_command(cmd, cwd=None, shell=False):
    """Run command and return success status"""
    try:
        if isinstance(cmd, str):
            result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
        else:
            result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True, shell=shell)
        
        if result.returncode != 0:
            print(f"❌ Command failed: {cmd}")
            print(f"Error: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"❌ Exception running command: {e}")
        return False

def check_and_install_requirements():
    """Check and install Python/Node requirements"""
    print("🔍 Checking requirements...")
    
    # Check Python
    try:
        import sys
        print(f"✅ Python {sys.version.split()[0]} found")
    except:
        print("❌ Python not found. Please install Python 3.9+")
        return False
    
    # Check pip
    if not run_command([sys.executable, "-m", "pip", "--version"]):
        print("❌ pip not found")
        return False
    print("✅ pip found")
    
    # Check Node.js
    if not run_command(["node", "--version"]):
        print("❌ Node.js not found. Installing...")
        # Try to install Node.js automatically (this varies by OS)
        if sys.platform == "win32":
            print("Please install Node.js from https://nodejs.org and run this script again")
            return False
        elif sys.platform == "darwin":  # macOS
            if run_command(["brew", "--version"]):
                run_command(["brew", "install", "node"])
            else:
                print("Please install Node.js from https://nodejs.org")
                return False
        else:  # Linux
            run_command(["sudo", "apt-get", "update"])
            run_command(["sudo", "apt-get", "install", "-y", "nodejs", "npm"])
    else:
        print("✅ Node.js found")
    
    # Check npm
    if not run_command(["npm", "--version"]):
        print("❌ npm not found")
        return False
    print("✅ npm found")
    
    return True

def auto_setup():
    """Automatically set up the entire application"""
    print("🚀 Starting automatic setup...")
    
    # Create environment files
    print("📝 Creating environment files...")
    backend_env = Path("backend/.env")
    if not backend_env.exists():
        if Path("backend/.env.example").exists():
            import shutil
            shutil.copy("backend/.env.example", "backend/.env")
            print("✅ Created backend/.env")
    
    frontend_env = Path("frontend/.env")
    if not frontend_env.exists():
        if Path("frontend/.env.example").exists():
            import shutil
            shutil.copy("frontend/.env.example", "frontend/.env")
            print("✅ Created frontend/.env")
    
    # Setup backend
    print("🐍 Setting up backend...")
    os.chdir("backend")
    
    # Create virtual environment
    if not Path("venv").exists():
        print("Creating Python virtual environment...")
        if not run_command([sys.executable, "-m", "venv", "venv"]):
            print("❌ Failed to create virtual environment")
            return False
    
    # Install Python dependencies
    print("Installing Python dependencies...")
    if sys.platform == "win32":
        pip_cmd = "venv\\Scripts\\pip"
        python_cmd = "venv\\Scripts\\python"
    else:
        pip_cmd = "venv/bin/pip"
        python_cmd = "venv/bin/python"
    
    if not run_command([pip_cmd, "install", "-r", "requirements.txt"]):
        print("❌ Failed to install Python dependencies")
        return False
    
    # Initialize database
    print("Initializing database...")
    init_code = """
try:
    from app.db.database import engine
    from app.db import models
    models.Base.metadata.create_all(bind=engine)
    print('✅ Database initialized successfully!')
except Exception as e:
    print(f'❌ Database initialization failed: {e}')
"""
    
    with open("init_db.py", "w") as f:
        f.write(init_code)
    
    run_command([python_cmd, "init_db.py"])
    os.remove("init_db.py")
    
    os.chdir("..")
    
    # Setup frontend
    print("⚛️ Setting up frontend...")
    os.chdir("frontend")
    
    print("Installing Node.js dependencies...")
    if not run_command(["npm", "install"]):
        print("❌ Failed to install Node.js dependencies")
        return False
    
    os.chdir("..")
    
    print("✅ Setup completed successfully!")
    return True

def start_servers():
    """Start both servers automatically"""
    print("🚀 Starting servers...")
    
    # Start backend
    print("🐍 Starting backend server...")
    os.chdir("backend")
    
    if sys.platform == "win32":
        backend_cmd = ["venv\\Scripts\\python", "-m", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]
    else:
        backend_cmd = ["venv/bin/python", "-m", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]
    
    backend_process = subprocess.Popen(backend_cmd)
    os.chdir("..")
    
    # Wait for backend to start
    print("⏳ Waiting for backend to start...")
    time.sleep(5)
    
    # Start frontend
    print("⚛️ Starting frontend server...")
    os.chdir("frontend")
    frontend_process = subprocess.Popen(["npm", "run", "dev"])
    os.chdir("..")
    
    # Wait for frontend to start
    print("⏳ Waiting for frontend to start...")
    time.sleep(10)
    
    # Open browser
    print("🌐 Opening browser...")
    webbrowser.open("http://localhost:3000")
    
    print("\n" + "="*50)
    print("✅ NEW DARVAS BOX TRADING APP IS RUNNING!")
    print("="*50)
    print("📱 Frontend: http://localhost:3000")
    print("🔧 Backend API: http://localhost:8000")
    print("📚 API Docs: http://localhost:8000/docs")
    print("="*50)
    print("Press Ctrl+C to stop both servers")
    print("="*50)
    
    # Keep running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Stopping servers...")
        backend_process.terminate()
        frontend_process.terminate()
        print("✅ Servers stopped")

def main():
    """Main function - does everything automatically"""
    print("🚀 NEW DARVAS BOX TRADING APPLICATION")
    print("🤖 Automatic Installation & Setup")
    print("="*50)
    
    # Check if we're in the right directory
    if not Path("README.md").exists():
        print("❌ Please run this script from the project root directory")
        print("   (The directory containing README.md)")
        sys.exit(1)
    
    # Check and install requirements
    if not check_and_install_requirements():
        print("❌ Requirements check failed")
        sys.exit(1)
    
    # Auto setup
    if not auto_setup():
        print("❌ Setup failed")
        sys.exit(1)
    
    # Start servers
    start_servers()

if __name__ == "__main__":
    main()
