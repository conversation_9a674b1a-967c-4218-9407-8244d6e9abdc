import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { dashboardApi } from '../../services/api';

interface GTTOrder {
  id: number;
  stock_symbol: string;
  order_type: string;
  trigger_price: number;
  quantity: number;
  price: number;
  status: string;
  created_at: string;
  expires_at: string | null;
}

interface GTTData {
  gtt_orders: GTTOrder[];
  active_orders: number;
  triggered_orders: number;
  total_orders: number;
}

const GTTOrderManagement: React.FC = () => {
  const [data, setData] = useState<GTTData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [newOrder, setNewOrder] = useState({
    stock_symbol: '',
    order_type: 'BUY',
    trigger_price: '',
    quantity: '',
    price: '',
  });

  useEffect(() => {
    fetchGTTData();
  }, []);

  const fetchGTTData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getGTTOrders();
      setData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch GTT orders');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOrder = async () => {
    try {
      // TODO: Implement GTT order creation API call
      console.log('Creating GTT order:', newOrder);
      setDialogOpen(false);
      setNewOrder({
        stock_symbol: '',
        order_type: 'BUY',
        trigger_price: '',
        quantity: '',
        price: '',
      });
      await fetchGTTData();
    } catch (err: any) {
      setError('Failed to create GTT order');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'success';
      case 'TRIGGERED':
        return 'info';
      case 'CANCELLED':
        return 'error';
      case 'EXPIRED':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getOrderTypeColor = (orderType: string) => {
    return orderType === 'BUY' ? 'success' : 'error';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          GTT Order Management
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchGTTData}
            sx={{ mr: 2 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setDialogOpen(true)}
          >
            New GTT Order
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* GTT Summary */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Orders
              </Typography>
              <Typography variant="h5">
                {data?.total_orders || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active Orders
              </Typography>
              <Typography variant="h5" color="success.main">
                {data?.active_orders || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Triggered Orders
              </Typography>
              <Typography variant="h5" color="info.main">
                {data?.triggered_orders || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Success Rate
              </Typography>
              <Typography variant="h5">
                {data?.total_orders ? 
                  `${((data.triggered_orders / data.total_orders) * 100).toFixed(1)}%` : 
                  '--'
                }
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* GTT Orders Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            GTT Orders
          </Typography>
          
          {data?.gtt_orders && data.gtt_orders.length > 0 ? (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Stock Symbol</TableCell>
                    <TableCell>Order Type</TableCell>
                    <TableCell align="right">Trigger Price</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell align="right">Order Price</TableCell>
                    <TableCell align="center">Status</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Expires</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.gtt_orders.map((order) => (
                    <TableRow key={order.id} hover>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {order.stock_symbol}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={order.order_type}
                          color={getOrderTypeColor(order.order_type) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(order.trigger_price)}
                      </TableCell>
                      <TableCell align="right">
                        {order.quantity.toLocaleString()}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(order.price)}
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={order.status}
                          color={getStatusColor(order.status) as any}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(order.created_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {order.expires_at ? 
                          new Date(order.expires_at).toLocaleDateString() : 
                          'No expiry'
                        }
                      </TableCell>
                      <TableCell align="center">
                        <IconButton size="small" sx={{ mr: 1 }}>
                          <EditIcon />
                        </IconButton>
                        <IconButton size="small" color="error">
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box textAlign="center" py={4}>
              <ScheduleIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="textSecondary" gutterBottom>
                No GTT Orders Found
              </Typography>
              <Typography color="textSecondary" sx={{ mb: 3 }}>
                Create your first GTT (Good Till Triggered) order to automate your trading strategy.
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setDialogOpen(true)}
              >
                Create GTT Order
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Create GTT Order Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New GTT Order</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Stock Symbol"
              value={newOrder.stock_symbol}
              onChange={(e) => setNewOrder({...newOrder, stock_symbol: e.target.value})}
              sx={{ mb: 2 }}
            />
            
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Order Type</InputLabel>
              <Select
                value={newOrder.order_type}
                label="Order Type"
                onChange={(e) => setNewOrder({...newOrder, order_type: e.target.value})}
              >
                <MenuItem value="BUY">Buy</MenuItem>
                <MenuItem value="SELL">Sell</MenuItem>
              </Select>
            </FormControl>
            
            <TextField
              fullWidth
              label="Trigger Price"
              type="number"
              value={newOrder.trigger_price}
              onChange={(e) => setNewOrder({...newOrder, trigger_price: e.target.value})}
              sx={{ mb: 2 }}
            />
            
            <TextField
              fullWidth
              label="Quantity"
              type="number"
              value={newOrder.quantity}
              onChange={(e) => setNewOrder({...newOrder, quantity: e.target.value})}
              sx={{ mb: 2 }}
            />
            
            <TextField
              fullWidth
              label="Order Price"
              type="number"
              value={newOrder.price}
              onChange={(e) => setNewOrder({...newOrder, price: e.target.value})}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateOrder} variant="contained">
            Create Order
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GTTOrderManagement;
