import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  CircularProgress,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  PlayArrow as RunIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Assessment as ReportIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { dashboardApi } from '../../services/api';

interface BacktestResult {
  id: number;
  name: string;
  strategy: string;
  start_date: string;
  end_date: string;
  initial_capital: number;
  final_value: number;
  total_return: number;
  total_return_percentage: number;
  max_drawdown: number;
  sharpe_ratio: number;
  win_rate: number;
  total_trades: number;
  status: string;
  created_at: string;
}

const BackTesting: React.FC = () => {
  const [backtests, setBacktests] = useState<BacktestResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [running, setRunning] = useState(false);
  
  const [newBacktest, setNewBacktest] = useState({
    name: '',
    strategy: 'DARVAS_BOX',
    start_date: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // 1 year ago
    end_date: new Date(),
    initial_capital: 100000,
    symbols: '',
  });

  useEffect(() => {
    fetchBacktestData();
  }, []);

  const fetchBacktestData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getBacktesting();
      // Mock data since API returns placeholder
      setBacktests([
        {
          id: 1,
          name: 'Darvas Box Strategy - NIFTY 50',
          strategy: 'DARVAS_BOX',
          start_date: '2023-01-01',
          end_date: '2023-12-31',
          initial_capital: 100000,
          final_value: 125000,
          total_return: 25000,
          total_return_percentage: 25.0,
          max_drawdown: -8.5,
          sharpe_ratio: 1.45,
          win_rate: 65.5,
          total_trades: 45,
          status: 'COMPLETED',
          created_at: '2024-01-15T10:30:00Z',
        },
        {
          id: 2,
          name: 'Darvas Box - Tech Stocks',
          strategy: 'DARVAS_BOX',
          start_date: '2023-06-01',
          end_date: '2023-12-31',
          initial_capital: 50000,
          final_value: 58500,
          total_return: 8500,
          total_return_percentage: 17.0,
          max_drawdown: -12.3,
          sharpe_ratio: 1.12,
          win_rate: 58.3,
          total_trades: 24,
          status: 'COMPLETED',
          created_at: '2024-01-10T14:20:00Z',
        },
      ]);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch backtest data');
    } finally {
      setLoading(false);
    }
  };

  const runBacktest = async () => {
    try {
      setRunning(true);
      // TODO: Implement backtest execution
      console.log('Running backtest:', newBacktest);
      await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate API call
      setDialogOpen(false);
      await fetchBacktestData();
    } catch (err: any) {
      setError('Failed to run backtest');
    } finally {
      setRunning(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(2)}%`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'RUNNING':
        return 'info';
      case 'FAILED':
        return 'error';
      default:
        return 'default';
    }
  };

  const getReturnColor = (returnValue: number) => {
    return returnValue >= 0 ? 'success.main' : 'error.main';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4">
            Back Testing
          </Typography>
          <Box>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchBacktestData}
              sx={{ mr: 2 }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setDialogOpen(true)}
            >
              New Backtest
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Backtest Summary */}
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Backtests
                </Typography>
                <Typography variant="h5">
                  {backtests.length}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Avg Return
                </Typography>
                <Typography variant="h5" color="success.main">
                  {backtests.length > 0 ? 
                    formatPercentage(backtests.reduce((sum, bt) => sum + bt.total_return_percentage, 0) / backtests.length) :
                    '0%'
                  }
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Avg Sharpe Ratio
                </Typography>
                <Typography variant="h5">
                  {backtests.length > 0 ? 
                    (backtests.reduce((sum, bt) => sum + bt.sharpe_ratio, 0) / backtests.length).toFixed(2) :
                    '0.00'
                  }
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Avg Win Rate
                </Typography>
                <Typography variant="h5">
                  {backtests.length > 0 ? 
                    `${(backtests.reduce((sum, bt) => sum + bt.win_rate, 0) / backtests.length).toFixed(1)}%` :
                    '0%'
                  }
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Backtests Table */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Backtest Results
            </Typography>
            
            {backtests.length > 0 ? (
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Period</TableCell>
                      <TableCell align="right">Initial Capital</TableCell>
                      <TableCell align="right">Final Value</TableCell>
                      <TableCell align="right">Return</TableCell>
                      <TableCell align="right">Return %</TableCell>
                      <TableCell align="right">Max Drawdown</TableCell>
                      <TableCell align="right">Sharpe Ratio</TableCell>
                      <TableCell align="right">Win Rate</TableCell>
                      <TableCell align="center">Status</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {backtests.map((backtest) => (
                      <TableRow key={backtest.id} hover>
                        <TableCell>
                          <Typography variant="subtitle2">
                            {backtest.name}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {backtest.strategy}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(backtest.start_date).toLocaleDateString()} - {new Date(backtest.end_date).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(backtest.initial_capital)}
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(backtest.final_value)}
                        </TableCell>
                        <TableCell 
                          align="right"
                          sx={{ color: getReturnColor(backtest.total_return) }}
                        >
                          {formatCurrency(backtest.total_return)}
                        </TableCell>
                        <TableCell 
                          align="right"
                          sx={{ color: getReturnColor(backtest.total_return_percentage) }}
                        >
                          {formatPercentage(backtest.total_return_percentage)}
                        </TableCell>
                        <TableCell align="right" sx={{ color: 'error.main' }}>
                          {formatPercentage(backtest.max_drawdown)}
                        </TableCell>
                        <TableCell align="right">
                          {backtest.sharpe_ratio.toFixed(2)}
                        </TableCell>
                        <TableCell align="right">
                          {backtest.win_rate.toFixed(1)}%
                        </TableCell>
                        <TableCell align="center">
                          <Chip
                            label={backtest.status}
                            color={getStatusColor(backtest.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Button
                            size="small"
                            startIcon={<ReportIcon />}
                            variant="outlined"
                          >
                            View Report
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Box textAlign="center" py={4}>
                <ReportIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="textSecondary" gutterBottom>
                  No Backtests Found
                </Typography>
                <Typography color="textSecondary" sx={{ mb: 3 }}>
                  Create your first backtest to analyze the performance of your trading strategy.
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setDialogOpen(true)}
                >
                  Create Backtest
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Create Backtest Dialog */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Create New Backtest</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ pt: 2 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Backtest Name"
                  value={newBacktest.name}
                  onChange={(e) => setNewBacktest({...newBacktest, name: e.target.value})}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Strategy</InputLabel>
                  <Select
                    value={newBacktest.strategy}
                    label="Strategy"
                    onChange={(e) => setNewBacktest({...newBacktest, strategy: e.target.value})}
                  >
                    <MenuItem value="DARVAS_BOX">Darvas Box</MenuItem>
                    <MenuItem value="CUSTOM">Custom Strategy</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Initial Capital"
                  type="number"
                  value={newBacktest.initial_capital}
                  onChange={(e) => setNewBacktest({...newBacktest, initial_capital: parseInt(e.target.value)})}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Start Date"
                  value={newBacktest.start_date}
                  onChange={(date) => setNewBacktest({...newBacktest, start_date: date || new Date()})}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="End Date"
                  value={newBacktest.end_date}
                  onChange={(date) => setNewBacktest({...newBacktest, end_date: date || new Date()})}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Stock Symbols (comma separated)"
                  value={newBacktest.symbols}
                  onChange={(e) => setNewBacktest({...newBacktest, symbols: e.target.value})}
                  placeholder="RELIANCE, TCS, INFY, HDFC"
                  helperText="Leave empty to use all available stocks"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button 
              onClick={runBacktest} 
              variant="contained" 
              startIcon={<RunIcon />}
              disabled={running}
            >
              {running ? 'Running...' : 'Run Backtest'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default BackTesting;
