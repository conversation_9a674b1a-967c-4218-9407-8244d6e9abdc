import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  PlayArrow as GenerateIcon,
  Refresh as RefreshIcon,
  TrendingUp,
  TrendingDown,
  Info as InfoIcon,
} from '@mui/icons-material';
import { dashboardApi } from '../../services/api';

interface Signal {
  id: number;
  stock_symbol: string;
  stock_name: string;
  signal_type: string;
  signal_strength: number;
  price: number;
  signal_date: string;
  created_at: string;
}

interface SignalData {
  signals: Signal[];
  buy_signals: number;
  sell_signals: number;
  total_signals: number;
}

const SignalGeneration: React.FC = () => {
  const [data, setData] = useState<SignalData | null>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSignalData();
  }, []);

  const fetchSignalData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getWeeklyHighSignals();
      setData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch signal data');
    } finally {
      setLoading(false);
    }
  };

  const generateSignals = async () => {
    try {
      setGenerating(true);
      // TODO: Implement signal generation API call
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      await fetchSignalData();
    } catch (err: any) {
      setError('Failed to generate signals');
    } finally {
      setGenerating(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const getSignalColor = (signalType: string) => {
    switch (signalType) {
      case 'BUY':
        return 'success';
      case 'SELL':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStrengthColor = (strength: number) => {
    if (strength >= 0.8) return 'success';
    if (strength >= 0.6) return 'warning';
    return 'default';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Signal Generation
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchSignalData}
            sx={{ mr: 2 }}
            disabled={generating}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<GenerateIcon />}
            onClick={generateSignals}
            disabled={generating}
          >
            {generating ? 'Generating...' : 'Generate Signals'}
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Signal Summary */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Signals
              </Typography>
              <Typography variant="h5">
                {data?.total_signals || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Buy Signals
              </Typography>
              <Typography variant="h5" color="success.main">
                {data?.buy_signals || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Sell Signals
              </Typography>
              <Typography variant="h5" color="error.main">
                {data?.sell_signals || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Success Rate
              </Typography>
              <Typography variant="h5">
                --% {/* TODO: Calculate from historical data */}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Signals Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Recent Signals
          </Typography>
          
          {data?.signals && data.signals.length > 0 ? (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Stock</TableCell>
                    <TableCell>Signal Type</TableCell>
                    <TableCell align="right">Price</TableCell>
                    <TableCell align="center">Strength</TableCell>
                    <TableCell>Signal Date</TableCell>
                    <TableCell>Generated</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.signals.map((signal) => (
                    <TableRow key={signal.id} hover>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2" fontWeight="bold">
                            {signal.stock_symbol}
                          </Typography>
                          <Typography variant="body2" color="textSecondary" noWrap>
                            {signal.stock_name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={signal.signal_type}
                          color={getSignalColor(signal.signal_type) as any}
                          icon={signal.signal_type === 'BUY' ? <TrendingUp /> : <TrendingDown />}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(signal.price)}
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={`${(signal.signal_strength * 100).toFixed(0)}%`}
                          color={getStrengthColor(signal.signal_strength) as any}
                          variant="outlined"
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(signal.signal_date).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {new Date(signal.created_at).toLocaleString()}
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title="View Details">
                          <IconButton size="small">
                            <InfoIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box textAlign="center" py={4}>
              <GenerateIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="textSecondary" gutterBottom>
                No Signals Generated
              </Typography>
              <Typography color="textSecondary" sx={{ mb: 3 }}>
                Click "Generate Signals" to analyze stocks and create trading signals based on weekly high patterns.
              </Typography>
              <Button
                variant="contained"
                startIcon={<GenerateIcon />}
                onClick={generateSignals}
                disabled={generating}
              >
                {generating ? 'Generating...' : 'Generate First Signals'}
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Signal Generation Info */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Signal Generation Logic
          </Typography>
          <Typography variant="body2" color="textSecondary" paragraph>
            The signal generation system analyzes stocks based on the New Darvas Box strategy:
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <Typography component="li" variant="body2" color="textSecondary">
              <strong>Buy Signal:</strong> Generated when a stock breaks above its weekly high with strong volume
            </Typography>
            <Typography component="li" variant="body2" color="textSecondary">
              <strong>Sell Signal:</strong> Generated when a stock falls below key support levels or shows weakness
            </Typography>
            <Typography component="li" variant="body2" color="textSecondary">
              <strong>Signal Strength:</strong> Based on volume, price momentum, and technical indicators
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SignalGeneration;
