#!/bin/bash

# Start Development Servers Script
echo "🚀 Starting New Darvas Box Trading Application..."

# Function to check if port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  Port $1 is already in use"
        return 1
    else
        return 0
    fi
}

# Check if setup has been run
if [ ! -f "backend/.env" ] || [ ! -f "frontend/.env" ]; then
    echo "❌ Environment files not found. Running setup first..."
    chmod +x setup.sh
    ./setup.sh
fi

# Check if backend dependencies are installed
if [ ! -d "backend/venv" ]; then
    echo "❌ Backend not set up. Running setup first..."
    chmod +x setup.sh
    ./setup.sh
fi

# Check if frontend dependencies are installed
if [ ! -d "frontend/node_modules" ]; then
    echo "❌ Frontend not set up. Running setup first..."
    chmod +x setup.sh
    ./setup.sh
fi

# Check ports
echo "🔍 Checking ports..."
check_port 8000 || echo "Backend port 8000 is busy - you may need to stop existing backend"
check_port 3000 || echo "Frontend port 3000 is busy - you may need to stop existing frontend"

# Start backend in background
echo "🐍 Starting backend server..."
cd backend
source venv/bin/activate
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!
echo "Backend started with PID: $BACKEND_PID"
cd ..

# Wait a moment for backend to start
sleep 3

# Start frontend
echo "⚛️  Starting frontend server..."
cd frontend
npm run dev &
FRONTEND_PID=$!
echo "Frontend started with PID: $FRONTEND_PID"
cd ..

echo ""
echo "✅ Both servers are starting up..."
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop both servers"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Servers stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
wait
