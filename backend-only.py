#!/usr/bin/env python3
"""
Backend-only installer for New Darvas Box Trading Application
This runs just the backend API without the frontend
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run command and return success status"""
    try:
        result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True, shell=True)
        if result.returncode != 0:
            print(f"❌ Command failed: {cmd}")
            print(f"Error: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"❌ Exception running command: {e}")
        return False

def setup_backend():
    """Set up backend only"""
    print("🐍 Setting up backend...")
    
    # Create environment file
    backend_env = Path("backend/.env")
    if not backend_env.exists():
        if Path("backend/.env.example").exists():
            import shutil
            shutil.copy("backend/.env.example", "backend/.env")
            print("✅ Created backend/.env")
    
    os.chdir("backend")
    
    # Create virtual environment
    if not Path("venv").exists():
        print("Creating Python virtual environment...")
        if not run_command(f"{sys.executable} -m venv venv"):
            print("❌ Failed to create virtual environment")
            return False
    
    # Install dependencies
    print("Installing Python dependencies...")
    if sys.platform == "win32":
        pip_cmd = "venv\\Scripts\\pip"
        python_cmd = "venv\\Scripts\\python"
    else:
        pip_cmd = "venv/bin/pip"
        python_cmd = "venv/bin/python"
    
    if not run_command(f"{pip_cmd} install -r requirements.txt"):
        print("❌ Failed to install Python dependencies")
        return False
    
    # Initialize database
    print("Initializing database...")
    init_code = """
try:
    from app.db.database import engine
    from app.db import models
    models.Base.metadata.create_all(bind=engine)
    print('✅ Database initialized successfully!')
except Exception as e:
    print(f'❌ Database initialization failed: {e}')
"""
    
    with open("init_db.py", "w") as f:
        f.write(init_code)
    
    run_command(f"{python_cmd} init_db.py")
    if Path("init_db.py").exists():
        os.remove("init_db.py")
    
    os.chdir("..")
    print("✅ Backend setup completed!")
    return True

def start_backend():
    """Start backend server"""
    print("🚀 Starting backend server...")
    
    os.chdir("backend")
    
    if sys.platform == "win32":
        cmd = "venv\\Scripts\\python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
    else:
        cmd = "venv/bin/python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
    
    print("⏳ Starting server...")
    process = subprocess.Popen(cmd, shell=True)
    
    # Wait for server to start
    time.sleep(5)
    
    print("\n" + "="*60)
    print("✅ NEW DARVAS BOX TRADING API IS RUNNING!")
    print("="*60)
    print("🔧 Backend API: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("📊 Interactive API: http://localhost:8000/redoc")
    print("="*60)
    print("🌐 Opening API documentation in browser...")
    print("Press Ctrl+C to stop the server")
    print("="*60)
    
    # Open browser to API docs
    webbrowser.open("http://localhost:8000/docs")
    
    os.chdir("..")
    
    # Keep running
    try:
        process.wait()
    except KeyboardInterrupt:
        print("\n🛑 Stopping server...")
        process.terminate()
        print("✅ Server stopped")

def main():
    """Main function"""
    print("🚀 NEW DARVAS BOX TRADING APPLICATION")
    print("🔧 Backend-Only Setup (No Frontend Required)")
    print("="*50)
    
    # Check if we're in the right directory
    if not Path("README.md").exists():
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    # Check Python
    try:
        print(f"✅ Python {sys.version.split()[0]} found")
    except:
        print("❌ Python not found")
        sys.exit(1)
    
    # Setup backend
    if not setup_backend():
        print("❌ Backend setup failed")
        sys.exit(1)
    
    # Start backend
    start_backend()

if __name__ == "__main__":
    main()
