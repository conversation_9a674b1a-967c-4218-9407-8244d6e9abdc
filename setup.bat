@echo off
echo 🚀 Setting up New Darvas Box Trading Application...

REM Check if we're in the right directory
if not exist "README.md" (
    echo ❌ Please run this script from the project root directory
    pause
    exit /b 1
)

REM Create environment files
echo 📝 Creating environment files...
if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env" >nul
    echo ✅ Created backend/.env
) else (
    echo ℹ️  backend/.env already exists
)

if not exist "frontend\.env" (
    copy "frontend\.env.example" "frontend\.env" >nul
    echo ✅ Created frontend/.env
) else (
    echo ℹ️  frontend/.env already exists
)

REM Setup backend
echo 🐍 Setting up backend...
cd backend

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install Python dependencies
echo Installing Python dependencies...
pip install -r requirements.txt

REM Initialize database
echo Initializing database...
python -c "from app.db.database import engine; from app.db import models; models.Base.metadata.create_all(bind=engine); print('Database initialized successfully!')"

cd ..

REM Setup frontend
echo ⚛️  Setting up frontend...
cd frontend

REM Install Node.js dependencies
echo Installing Node.js dependencies...
npm install

cd ..

echo ✅ Setup completed successfully!
echo.
echo 🚀 To start the application:
echo 1. Start backend:  cd backend ^&^& venv\Scripts\activate ^&^& uvicorn app.main:app --reload
echo 2. Start frontend: cd frontend ^&^& npm run dev
echo.
echo 📱 Access the application at: http://localhost:3000
echo 📚 API documentation at: http://localhost:8000/docs
pause
