#!/usr/bin/env python3
"""
Quick Backend Starter for New Darvas Box Trading Application
Gets you up and running in 30 seconds!
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def run_command(cmd, cwd=None, show_output=True):
    """Run command and return success status"""
    try:
        if show_output:
            print(f"🔄 Running: {cmd}")
        
        result = subprocess.run(cmd, cwd=cwd, shell=True, capture_output=not show_output, text=True)
        
        if result.returncode != 0:
            if not show_output:
                print(f"❌ Command failed: {cmd}")
                print(f"Error: {result.stderr}")
            return False
        
        if not show_output and result.stdout:
            print(result.stdout.strip())
        
        return True
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def quick_backend_setup():
    """Super quick backend setup"""
    print("🚀 QUICK BACKEND SETUP - 30 SECONDS!")
    print("=" * 40)
    
    # Check if we're in the right place
    if not Path("backend").exists():
        print("❌ Backend directory not found!")
        return False
    
    # Create .env file
    backend_env = Path("backend/.env")
    if not backend_env.exists():
        env_example = Path("backend/.env.example")
        if env_example.exists():
            import shutil
            shutil.copy(str(env_example), str(backend_env))
            print("✅ Created backend/.env")
        else:
            # Create a minimal .env file
            with open(backend_env, "w") as f:
                f.write("""DATABASE_URL=sqlite:///./darvas_box.db
SECRET_KEY=dev-secret-key-change-in-production
DEBUG=True
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
""")
            print("✅ Created minimal backend/.env")
    
    # Go to backend directory
    os.chdir("backend")
    
    # Create virtual environment if it doesn't exist
    if not Path("venv").exists():
        print("📦 Creating Python virtual environment...")
        if not run_command(f'"{sys.executable}" -m venv venv', show_output=False):
            print("❌ Failed to create virtual environment")
            return False
        print("✅ Virtual environment created")
    
    # Install dependencies
    print("📥 Installing Python dependencies...")
    pip_cmd = "venv\\Scripts\\pip" if sys.platform == "win32" else "venv/bin/pip"
    
    if not run_command(f'"{pip_cmd}" install -r requirements.txt', show_output=False):
        print("❌ Failed to install dependencies")
        return False
    print("✅ Dependencies installed")
    
    # Initialize database
    print("🗄️ Setting up database...")
    python_cmd = "venv\\Scripts\\python" if sys.platform == "win32" else "venv/bin/python"
    
    # Create a simple database initialization script
    init_script = '''
import sys
import os
sys.path.append(os.getcwd())

try:
    from app.db.database import engine
    from app.db import models
    models.Base.metadata.create_all(bind=engine)
    print("✅ Database initialized successfully!")
except Exception as e:
    print(f"❌ Database error: {e}")
    sys.exit(1)
'''
    
    with open("quick_init.py", "w") as f:
        f.write(init_script)
    
    if not run_command(f'"{python_cmd}" quick_init.py', show_output=False):
        print("❌ Database initialization failed")
        return False
    
    # Clean up
    if Path("quick_init.py").exists():
        os.remove("quick_init.py")
    
    print("✅ Database ready")
    
    return True

def start_backend_server():
    """Start the backend server"""
    print("\n🚀 STARTING BACKEND SERVER...")
    print("=" * 40)
    
    python_cmd = "venv\\Scripts\\python" if sys.platform == "win32" else "venv/bin/python"
    cmd = f'"{python_cmd}" -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000'
    
    print("⏳ Starting server (this may take a moment)...")
    
    # Start server in background
    process = subprocess.Popen(cmd, shell=True)
    
    # Wait for server to start
    print("⏳ Waiting for server to initialize...")
    time.sleep(8)
    
    # Check if server is running
    try:
        import urllib.request
        urllib.request.urlopen("http://localhost:8000/health", timeout=5)
        server_running = True
    except:
        server_running = False
    
    print("\n" + "=" * 60)
    print("🎉 NEW DARVAS BOX TRADING API IS LIVE!")
    print("=" * 60)
    print("🔧 Backend API: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("📊 Alternative Docs: http://localhost:8000/redoc")
    print("❤️ Health Check: http://localhost:8000/health")
    print("=" * 60)
    
    if server_running:
        print("✅ Server is responding!")
    else:
        print("⚠️ Server may still be starting up...")
    
    print("\n🌐 Opening API documentation in your browser...")
    print("💡 You can test all the trading endpoints from the docs!")
    print("\n📋 Available Endpoints:")
    print("   • /dashboard/overview - Portfolio summary")
    print("   • /stocks/search - Search stocks")
    print("   • /portfolio/ - Portfolio management")
    print("   • /signals/ - Trading signals")
    print("   • /broker/ - Broker integration")
    print("\n⚠️ Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Open browser
    time.sleep(2)
    webbrowser.open("http://localhost:8000/docs")
    
    # Keep server running
    try:
        process.wait()
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping server...")
        process.terminate()
        time.sleep(2)
        print("✅ Server stopped successfully!")
        print("🔄 Run this script again anytime to restart!")

def main():
    """Main function"""
    print("🚀 NEW DARVAS BOX TRADING APPLICATION")
    print("⚡ QUICK BACKEND LAUNCHER")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("README.md").exists():
        print("❌ Please run this script from the project root directory")
        print("   (The directory containing README.md)")
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Quick setup
    if not quick_backend_setup():
        print("❌ Setup failed!")
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Start server
    start_backend_server()

if __name__ == "__main__":
    main()
