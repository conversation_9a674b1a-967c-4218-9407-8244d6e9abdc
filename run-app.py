#!/usr/bin/env python3
"""
New Darvas Box Trading Application - One-Click Launcher
This script sets up and runs both backend and frontend servers
"""

import os
import sys
import subprocess
import time
import signal
import platform
from pathlib import Path

class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_colored(message, color=Colors.OKGREEN):
    print(f"{color}{message}{Colors.ENDC}")

def print_header():
    print_colored("🚀 New Darvas Box Trading Application", Colors.HEADER)
    print_colored("=" * 50, Colors.HEADER)

def check_requirements():
    """Check if Python and Node.js are installed"""
    print_colored("🔍 Checking requirements...")
    
    # Check Python
    try:
        python_version = subprocess.check_output([sys.executable, "--version"], text=True).strip()
        print_colored(f"✅ {python_version}", Colors.OKGREEN)
    except:
        print_colored("❌ Python not found", Colors.FAIL)
        return False
    
    # Check Node.js
    try:
        node_version = subprocess.check_output(["node", "--version"], text=True).strip()
        print_colored(f"✅ Node.js {node_version}", Colors.OKGREEN)
    except:
        print_colored("❌ Node.js not found. Please install Node.js 16+", Colors.FAIL)
        return False
    
    # Check npm
    try:
        npm_version = subprocess.check_output(["npm", "--version"], text=True).strip()
        print_colored(f"✅ npm {npm_version}", Colors.OKGREEN)
    except:
        print_colored("❌ npm not found", Colors.FAIL)
        return False
    
    return True

def setup_environment():
    """Set up environment files"""
    print_colored("📝 Setting up environment files...")
    
    # Backend .env
    backend_env = Path("backend/.env")
    if not backend_env.exists():
        subprocess.run(["cp" if platform.system() != "Windows" else "copy", 
                       "backend/.env.example", "backend/.env"], shell=True)
        print_colored("✅ Created backend/.env", Colors.OKGREEN)
    else:
        print_colored("ℹ️  backend/.env already exists", Colors.OKCYAN)
    
    # Frontend .env
    frontend_env = Path("frontend/.env")
    if not frontend_env.exists():
        subprocess.run(["cp" if platform.system() != "Windows" else "copy", 
                       "frontend/.env.example", "frontend/.env"], shell=True)
        print_colored("✅ Created frontend/.env", Colors.OKGREEN)
    else:
        print_colored("ℹ️  frontend/.env already exists", Colors.OKCYAN)

def setup_backend():
    """Set up backend environment and dependencies"""
    print_colored("🐍 Setting up backend...")
    
    os.chdir("backend")
    
    # Create virtual environment
    venv_path = Path("venv")
    if not venv_path.exists():
        print_colored("Creating Python virtual environment...", Colors.OKCYAN)
        subprocess.run([sys.executable, "-m", "venv", "venv"])
    
    # Activate virtual environment and install dependencies
    if platform.system() == "Windows":
        pip_cmd = "venv\\Scripts\\pip"
        python_cmd = "venv\\Scripts\\python"
    else:
        pip_cmd = "venv/bin/pip"
        python_cmd = "venv/bin/python"
    
    print_colored("Installing Python dependencies...", Colors.OKCYAN)
    subprocess.run([pip_cmd, "install", "-r", "requirements.txt"])
    
    # Initialize database
    print_colored("Initializing database...", Colors.OKCYAN)
    init_db_code = """
from app.db.database import engine
from app.db import models
models.Base.metadata.create_all(bind=engine)
print('Database initialized successfully!')
"""
    subprocess.run([python_cmd, "-c", init_db_code])
    
    os.chdir("..")
    print_colored("✅ Backend setup complete", Colors.OKGREEN)

def setup_frontend():
    """Set up frontend dependencies"""
    print_colored("⚛️  Setting up frontend...")
    
    os.chdir("frontend")
    
    # Install npm dependencies
    print_colored("Installing Node.js dependencies...", Colors.OKCYAN)
    subprocess.run(["npm", "install"])
    
    os.chdir("..")
    print_colored("✅ Frontend setup complete", Colors.OKGREEN)

def start_servers():
    """Start both backend and frontend servers"""
    print_colored("🚀 Starting servers...", Colors.HEADER)
    
    # Start backend
    print_colored("🐍 Starting backend server...", Colors.OKCYAN)
    os.chdir("backend")
    
    if platform.system() == "Windows":
        backend_cmd = ["venv\\Scripts\\python", "-m", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]
    else:
        backend_cmd = ["venv/bin/python", "-m", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]
    
    backend_process = subprocess.Popen(backend_cmd)
    os.chdir("..")
    
    # Wait for backend to start
    time.sleep(3)
    
    # Start frontend
    print_colored("⚛️  Starting frontend server...", Colors.OKCYAN)
    os.chdir("frontend")
    frontend_process = subprocess.Popen(["npm", "run", "dev"])
    os.chdir("..")
    
    print_colored("\n✅ Both servers are running!", Colors.OKGREEN)
    print_colored("📱 Frontend: http://localhost:3000", Colors.OKCYAN)
    print_colored("🔧 Backend API: http://localhost:8000", Colors.OKCYAN)
    print_colored("📚 API Docs: http://localhost:8000/docs", Colors.OKCYAN)
    print_colored("\nPress Ctrl+C to stop both servers", Colors.WARNING)
    
    # Handle shutdown
    def signal_handler(sig, frame):
        print_colored("\n🛑 Stopping servers...", Colors.WARNING)
        backend_process.terminate()
        frontend_process.terminate()
        print_colored("✅ Servers stopped", Colors.OKGREEN)
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    # Wait for processes
    try:
        backend_process.wait()
        frontend_process.wait()
    except KeyboardInterrupt:
        signal_handler(None, None)

def main():
    """Main function"""
    print_header()
    
    # Check if we're in the right directory
    if not Path("README.md").exists():
        print_colored("❌ Please run this script from the project root directory", Colors.FAIL)
        sys.exit(1)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Check if setup is needed
    if not Path("backend/venv").exists() or not Path("frontend/node_modules").exists():
        print_colored("🔧 First-time setup required...", Colors.WARNING)
        setup_backend()
        setup_frontend()
    else:
        print_colored("ℹ️  Environment already set up", Colors.OKCYAN)
    
    # Start servers
    start_servers()

if __name__ == "__main__":
    main()
