import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  CircularProgress,
  Alert,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Switch,
} from '@mui/material';
import {
  Link as ConnectIcon,
  LinkOff as DisconnectIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Security as SecurityIcon,
  AccountBalance as AccountIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { dashboardApi } from '../../services/api';

interface BrokerConnectionData {
  broker_name: string | null;
  is_connected: boolean;
  last_connected: string | null;
  connection_status: string;
  client_code: string | null;
}

const ConnectBroker: React.FC = () => {
  const [connectionData, setConnectionData] = useState<BrokerConnectionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  
  const [credentials, setCredentials] = useState({
    api_key: '',
    client_code: '',
    pin: '',
    totp_secret: '',
    save_credentials: false,
  });

  useEffect(() => {
    fetchConnectionData();
  }, []);

  const fetchConnectionData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getBrokerConnection();
      setConnectionData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch broker connection data');
    } finally {
      setLoading(false);
    }
  };

  const connectBroker = async () => {
    try {
      setConnecting(true);
      // TODO: Implement broker connection API call
      console.log('Connecting to broker with:', credentials);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      setDialogOpen(false);
      await fetchConnectionData();
    } catch (err: any) {
      setError('Failed to connect to broker');
    } finally {
      setConnecting(false);
    }
  };

  const disconnectBroker = async () => {
    try {
      setConnecting(true);
      // TODO: Implement broker disconnection API call
      console.log('Disconnecting from broker');
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      await fetchConnectionData();
    } catch (err: any) {
      setError('Failed to disconnect from broker');
    } finally {
      setConnecting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'connected':
        return 'success';
      case 'disconnected':
        return 'error';
      case 'connecting':
        return 'info';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Connect Broker
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchConnectionData}
        >
          Refresh Status
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Connection Status */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <AccountIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Angel Broking Connection
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Typography>Status:</Typography>
                  <Chip
                    label={connectionData?.connection_status || 'Unknown'}
                    color={getStatusColor(connectionData?.connection_status || '') as any}
                    icon={connectionData?.is_connected ? <CheckIcon /> : <ErrorIcon />}
                  />
                </Box>
                
                {connectionData?.client_code && (
                  <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                    <Typography>Client Code:</Typography>
                    <Typography variant="subtitle2">
                      {connectionData.client_code}
                    </Typography>
                  </Box>
                )}
                
                {connectionData?.last_connected && (
                  <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                    <Typography>Last Connected:</Typography>
                    <Typography variant="body2" color="textSecondary">
                      {new Date(connectionData.last_connected).toLocaleString()}
                    </Typography>
                  </Box>
                )}
                
                <Divider sx={{ my: 2 }} />
                
                <Box display="flex" gap={2}>
                  {connectionData?.is_connected ? (
                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<DisconnectIcon />}
                      onClick={disconnectBroker}
                      disabled={connecting}
                      fullWidth
                    >
                      {connecting ? 'Disconnecting...' : 'Disconnect'}
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      startIcon={<ConnectIcon />}
                      onClick={() => setDialogOpen(true)}
                      disabled={connecting}
                      fullWidth
                    >
                      Connect to Angel Broking
                    </Button>
                  )}
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Connection Features */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Features Available
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color={connectionData?.is_connected ? 'success' : 'disabled'} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Real-time Market Data"
                    secondary="Live stock prices and market updates"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color={connectionData?.is_connected ? 'success' : 'disabled'} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Order Placement"
                    secondary="Buy and sell orders directly from the platform"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color={connectionData?.is_connected ? 'success' : 'disabled'} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="GTT Orders"
                    secondary="Good Till Triggered order management"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color={connectionData?.is_connected ? 'success' : 'disabled'} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Portfolio Sync"
                    secondary="Automatic synchronization of holdings"
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color={connectionData?.is_connected ? 'success' : 'disabled'} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Historical Data"
                    secondary="Access to historical price data"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Setup Instructions */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Setup Instructions
              </Typography>
              
              <Typography variant="body2" color="textSecondary" paragraph>
                To connect to Angel Broking, you'll need the following credentials from your Angel Broking account:
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Required Information:
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText 
                        primary="API Key"
                        secondary="Obtained from Angel Broking developer portal"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Client Code"
                        secondary="Your Angel Broking client ID"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="PIN"
                        secondary="Your trading PIN"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="TOTP Secret"
                        secondary="Time-based OTP secret key"
                      />
                    </ListItem>
                  </List>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Security Notes:
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemIcon>
                        <SecurityIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Credentials are encrypted and stored securely"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <SecurityIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="Connection uses official Angel Broking APIs"
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <SecurityIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary="You can disconnect at any time"
                      />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Connection Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Connect to Angel Broking</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="API Key"
              type="password"
              value={credentials.api_key}
              onChange={(e) => setCredentials({...credentials, api_key: e.target.value})}
              sx={{ mb: 2 }}
              helperText="Your Angel Broking API key"
            />
            
            <TextField
              fullWidth
              label="Client Code"
              value={credentials.client_code}
              onChange={(e) => setCredentials({...credentials, client_code: e.target.value})}
              sx={{ mb: 2 }}
              helperText="Your Angel Broking client ID"
            />
            
            <TextField
              fullWidth
              label="PIN"
              type="password"
              value={credentials.pin}
              onChange={(e) => setCredentials({...credentials, pin: e.target.value})}
              sx={{ mb: 2 }}
              helperText="Your trading PIN"
            />
            
            <TextField
              fullWidth
              label="TOTP Secret"
              type="password"
              value={credentials.totp_secret}
              onChange={(e) => setCredentials({...credentials, totp_secret: e.target.value})}
              sx={{ mb: 2 }}
              helperText="Time-based OTP secret key"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={credentials.save_credentials}
                  onChange={(e) => setCredentials({...credentials, save_credentials: e.target.checked})}
                />
              }
              label="Save credentials securely for future sessions"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={connectBroker} 
            variant="contained"
            disabled={connecting || !credentials.api_key || !credentials.client_code || !credentials.pin || !credentials.totp_secret}
          >
            {connecting ? 'Connecting...' : 'Connect'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ConnectBroker;
