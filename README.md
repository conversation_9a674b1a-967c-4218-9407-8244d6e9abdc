# New Darvas Box Trading Application

A professional single-user web application for stock backtesting and automated trading using the Darvas Box strategy.

## Features

- **Capital Management**: Portfolio and risk management interface
- **Stock Universal**: Universal stock screening and analysis
- **BOH Filter**: Custom filtering system
- **Weekly High**: Weekly high analysis with signal generation, GTT order management, and holdings display
- **Back Testing**: Historical strategy testing interface
- **Connect Broker**: Angel Broking integration via SmartAPI

## Technology Stack

### Frontend
- **React 18** with TypeScript
- **Material-UI (MUI)** for modern, professional UI components
- **React Router** for navigation
- **Recharts** for data visualization
- **Axios** for API communication

### Backend
- **FastAPI** with Python 3.9+
- **SQLAlchemy** for database ORM
- **Pydantic** for data validation
- **Uvicorn** as ASGI server

### Database
- **SQLite** for single-user deployment
- **Alembic** for database migrations

### Data Sources
- **Yahoo Finance API** (yfinance) for stock data
- **Angel Broking SmartAPI** for broker integration

## Project Structure

```
new-darvas-box/
├── backend/
│   ├── app/
│   │   ├── api/
│   │   ├── core/
│   │   ├── db/
│   │   ├── models/
│   │   ├── schemas/
│   │   ├── services/
│   │   └── main.py
│   ├── requirements.txt
│   └── alembic/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   ├── utils/
│   │   └── App.tsx
│   ├── package.json
│   └── public/
├── smartapi-integration/
├── docs/
└── README.md
```

## Installation

### Prerequisites
- Python 3.9+
- Node.js 16+
- npm or yarn

### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### Frontend Setup
```bash
cd frontend
npm install
npm start
```

## Configuration

1. Copy `.env.example` to `.env` in both backend and frontend directories
2. Configure your API keys and database settings
3. Set up Angel Broking SmartAPI credentials

## Usage

1. Start the backend server: `uvicorn app.main:app --reload`
2. Start the frontend development server: `npm start`
3. Access the application at `http://localhost:3000`

## Development

### Adding New Features
1. Create API endpoints in `backend/app/api/`
2. Add database models in `backend/app/models/`
3. Create frontend components in `frontend/src/components/`
4. Add pages in `frontend/src/pages/`

### Database Migrations
```bash
cd backend
alembic revision --autogenerate -m "Description"
alembic upgrade head
```

## Deployment

This application is designed for single-user deployment. Each user should deploy their own instance.

### Docker Deployment (Recommended)
```bash
docker-compose up -d
```

### Manual Deployment
1. Build frontend: `npm run build`
2. Configure production database
3. Deploy backend with production ASGI server
4. Serve frontend static files

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please create an issue in the GitHub repository.
