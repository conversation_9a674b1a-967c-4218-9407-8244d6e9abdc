# New Darvas Box Trading Application

A professional single-user web application for stock backtesting and automated trading using the Darvas Box strategy.

## 🚀 Features

### Dashboard Modules
- **📊 Dashboard Overview**: Real-time portfolio summary and system status
- **💰 Capital Management**: Portfolio and risk management interface
- **🔍 Stock Universal**: Universal stock screening and analysis with market data
- **🎯 BOH Filter**: Custom filtering system (logic to be implemented)
- **📈 Weekly High Analysis**:
  - Signal Generation based on weekly high patterns
  - GTT (Good Till Triggered) Order Management
  - Current Holdings Display with P&L tracking
- **⚡ Back Testing**: Historical strategy testing with performance metrics
- **🔗 Connect Broker**: Angel Broking integration via SmartAPI

### Key Capabilities
- Real-time stock data from Yahoo Finance
- Professional trading interface with Material-UI
- Automated signal generation
- Portfolio tracking and management
- Broker integration for live trading
- Historical backtesting with detailed analytics
- Responsive design for desktop and mobile

## 🛠 Technology Stack

### Frontend
- **React 18** with TypeScript for type safety
- **Material-UI (MUI)** for modern, professional UI components
- **React Router** for client-side navigation
- **Recharts** for data visualization and charts
- **Axios** for API communication

### Backend
- **FastAPI** with Python 3.9+ for high-performance API
- **SQLAlchemy** for database ORM
- **Pydantic** for data validation and serialization
- **Uvicorn** as ASGI server

### Database
- **SQLite** for single-user deployment (easily upgradeable to PostgreSQL)
- **Alembic** for database migrations

### Data Sources & Integration
- **Yahoo Finance API** (yfinance) for real-time and historical stock data
- **Angel Broking SmartAPI** for broker integration and live trading

## Project Structure

```
new-darvas-box/
├── backend/
│   ├── app/
│   │   ├── api/
│   │   ├── core/
│   │   ├── db/
│   │   ├── models/
│   │   ├── schemas/
│   │   ├── services/
│   │   └── main.py
│   ├── requirements.txt
│   └── alembic/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   ├── utils/
│   │   └── App.tsx
│   ├── package.json
│   └── public/
├── smartapi-integration/
├── docs/
└── README.md
```

## 🚀 Quick Start

### Option 1: Docker (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd new-darvas-box

# Copy environment files
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Start with Docker Compose
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

### Option 2: Manual Setup

#### Prerequisites
- Python 3.9+
- Node.js 16+
- npm or yarn

#### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Copy and configure environment
cp .env.example .env
# Edit .env with your settings

# Initialize database
alembic upgrade head

# Start server
uvicorn app.main:app --reload
```

#### Frontend Setup
```bash
cd frontend
npm install

# Copy and configure environment
cp .env.example .env
# Edit .env with your settings

# Start development server
npm start
```

## 📋 Configuration

### Required Setup
1. **Environment Variables**: Copy `.env.example` to `.env` in both directories
2. **SmartAPI Integration**: Place the SmartAPI library in `smartapi-python-main/smartapi-python-main/`
3. **Angel Broking Credentials**: Configure in the Connect Broker section of the app

### Key Configuration Files
- `backend/.env` - Backend configuration (database, API keys, etc.)
- `frontend/.env` - Frontend configuration (API endpoints, app settings)

## 🎯 Usage

1. **Start the Application**
   - Backend: `uvicorn app.main:app --reload` (http://localhost:8000)
   - Frontend: `npm start` (http://localhost:3000)

2. **Access the Dashboard**
   - Navigate to http://localhost:3000
   - Explore different modules from the sidebar navigation

3. **Connect Your Broker**
   - Go to "Connect Broker" section
   - Enter your Angel Broking credentials
   - Enable live trading features

## 📁 Project Structure

```
new-darvas-box/
├── backend/                    # FastAPI backend
│   ├── app/
│   │   ├── api/               # API endpoints
│   │   ├── core/              # Core configuration
│   │   ├── db/                # Database models
│   │   ├── schemas/           # Pydantic schemas
│   │   ├── services/          # Business logic
│   │   └── main.py           # FastAPI app
│   ├── alembic/              # Database migrations
│   └── requirements.txt      # Python dependencies
├── frontend/                  # React frontend
│   ├── src/
│   │   ├── components/       # Reusable components
│   │   ├── pages/           # Dashboard pages
│   │   ├── services/        # API services
│   │   └── App.tsx          # Main app component
│   └── package.json         # Node.js dependencies
├── smartapi-python-main/     # Angel Broking SmartAPI
├── docker-compose.yml        # Docker configuration
└── README.md                # This file
```

## 🔧 Development

### Adding New Features
1. **Backend**: Create endpoints in `backend/app/api/`, add services in `backend/app/services/`
2. **Frontend**: Create components in `frontend/src/components/`, add pages in `frontend/src/pages/`
3. **Database**: Add models in `backend/app/db/models.py`, create migrations with Alembic

### Database Migrations
```bash
cd backend
alembic revision --autogenerate -m "Description"
alembic upgrade head
```

### Testing
```bash
# Backend tests
cd backend && pytest

# Frontend tests
cd frontend && npm test
```

## 🚀 Deployment

### Docker Deployment (Recommended)
```bash
# Production deployment
docker-compose -f docker-compose.prod.yml up -d
```

### Manual Deployment
1. **Build Frontend**: `cd frontend && npm run build`
2. **Configure Database**: Set up PostgreSQL for production
3. **Environment**: Set production environment variables
4. **Server**: Use Gunicorn + Uvicorn for backend
5. **Proxy**: Configure Nginx for frontend serving

## 📚 Documentation

- **[Installation Guide](INSTALLATION.md)** - Detailed setup instructions
- **[Development Guide](DEVELOPMENT.md)** - Development workflow and standards
- **[API Documentation](http://localhost:8000/docs)** - Interactive API docs (when running)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes following the code standards
4. Add tests for new functionality
5. Update documentation as needed
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: Create an issue in the GitHub repository
- **Documentation**: Check the [Installation](INSTALLATION.md) and [Development](DEVELOPMENT.md) guides
- **API Reference**: Visit http://localhost:8000/docs when running the backend

## ⚠️ Disclaimer

This application is for educational and personal use. Always test thoroughly before using with real money. Trading involves risk, and past performance does not guarantee future results.
