import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Chip,
  InputAdornment,
  Tab,
  Tabs,
} from '@mui/material';
import {
  Search as SearchIcon,
  TrendingUp,
  TrendingDown,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { dashboardApi } from '../../services/api';

interface Stock {
  symbol: string;
  name: string;
  current_price: number;
  previous_close: number;
  change: number;
  change_percentage: number;
  volume: number;
  market_cap: number;
}

interface StockUniversalData {
  top_gainers: Stock[];
  top_losers: Stock[];
  all_stocks: Stock[];
  market_summary: {
    total_stocks: number;
    gainers: number;
    losers: number;
    unchanged: number;
  };
}

const StockUniversal: React.FC = () => {
  const [data, setData] = useState<StockUniversalData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    fetchStockData();
  }, []);

  const fetchStockData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getStockUniversal();
      setData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch stock data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(2)}%`;
  };

  const formatVolume = (volume: number) => {
    if (volume >= 10000000) {
      return `${(volume / 10000000).toFixed(1)}Cr`;
    } else if (volume >= 100000) {
      return `${(volume / 100000).toFixed(1)}L`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toString();
  };

  const formatMarketCap = (marketCap: number) => {
    if (marketCap >= 10000000000) {
      return `₹${(marketCap / 10000000000).toFixed(1)}K Cr`;
    } else if (marketCap >= 100000000) {
      return `₹${(marketCap / 100000000).toFixed(1)} Cr`;
    }
    return `₹${marketCap.toLocaleString()}`;
  };

  const filteredStocks = data?.all_stocks.filter(stock =>
    stock.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
    stock.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const renderStockTable = (stocks: Stock[], title: string) => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
        <TableContainer component={Paper} elevation={0}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Symbol</TableCell>
                <TableCell>Name</TableCell>
                <TableCell align="right">Price</TableCell>
                <TableCell align="right">Change</TableCell>
                <TableCell align="right">Change %</TableCell>
                <TableCell align="right">Volume</TableCell>
                <TableCell align="right">Market Cap</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {stocks.map((stock) => (
                <TableRow key={stock.symbol} hover>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {stock.symbol}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" noWrap>
                      {stock.name}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    {formatCurrency(stock.current_price)}
                  </TableCell>
                  <TableCell 
                    align="right"
                    sx={{ color: stock.change >= 0 ? 'success.main' : 'error.main' }}
                  >
                    {stock.change >= 0 ? '+' : ''}{formatCurrency(stock.change)}
                  </TableCell>
                  <TableCell align="right">
                    <Chip
                      label={formatPercentage(stock.change_percentage)}
                      size="small"
                      color={stock.change_percentage >= 0 ? 'success' : 'error'}
                      icon={stock.change_percentage >= 0 ? <TrendingUp /> : <TrendingDown />}
                    />
                  </TableCell>
                  <TableCell align="right">
                    {formatVolume(stock.volume)}
                  </TableCell>
                  <TableCell align="right">
                    {formatMarketCap(stock.market_cap)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!data) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        No stock data available
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Stock Universal
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchStockData}
        >
          Refresh Data
        </Button>
      </Box>

      {/* Market Summary */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Stocks
              </Typography>
              <Typography variant="h5">
                {data.market_summary.total_stocks}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Gainers
              </Typography>
              <Typography variant="h5" color="success.main">
                {data.market_summary.gainers}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Losers
              </Typography>
              <Typography variant="h5" color="error.main">
                {data.market_summary.losers}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Unchanged
              </Typography>
              <Typography variant="h5">
                {data.market_summary.unchanged}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Tabs */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
              <Tab label="All Stocks" />
              <Tab label="Top Gainers" />
              <Tab label="Top Losers" />
            </Tabs>
            <TextField
              placeholder="Search stocks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ width: 300 }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Stock Tables */}
      {tabValue === 0 && renderStockTable(filteredStocks, `All Stocks (${filteredStocks.length})`)}
      {tabValue === 1 && renderStockTable(data.top_gainers, 'Top Gainers')}
      {tabValue === 2 && renderStockTable(data.top_losers, 'Top Losers')}
    </Box>
  );
};

export default StockUniversal;
