from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List
from app.db.database import get_db

router = APIRouter()


@router.get("/")
async def get_backtests(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """Get all backtests"""
    return {"message": "Backtests endpoint - to be implemented"}


@router.post("/")
async def create_backtest(db: Session = Depends(get_db)):
    """Create and run a new backtest"""
    return {"message": "Create backtest endpoint - to be implemented"}


@router.get("/{backtest_id}")
async def get_backtest(backtest_id: int, db: Session = Depends(get_db)):
    """Get backtest results"""
    return {"message": f"Get backtest {backtest_id} - to be implemented"}


@router.post("/{backtest_id}/run")
async def run_backtest(backtest_id: int, db: Session = Depends(get_db)):
    """Run backtest"""
    return {"message": f"Run backtest {backtest_id} - to be implemented"}


@router.get("/{backtest_id}/results")
async def get_backtest_results(backtest_id: int, db: Session = Depends(get_db)):
    """Get detailed backtest results"""
    return {"message": f"Get backtest results {backtest_id} - to be implemented"}
