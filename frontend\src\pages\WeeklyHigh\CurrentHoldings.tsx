import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  TrendingUp,
  TrendingDown,
  Inventory as HoldingsIcon,
} from '@mui/icons-material';
import { dashboardApi } from '../../services/api';

interface Holding {
  portfolio_name: string;
  stock_symbol: string;
  stock_name: string;
  quantity: number;
  average_price: number;
  current_price: number;
  market_value: number;
  unrealized_pnl: number;
  unrealized_pnl_percentage: number;
}

interface HoldingsData {
  holdings: Holding[];
  total_holdings: number;
  total_market_value: number;
  total_unrealized_pnl: number;
}

const CurrentHoldings: React.FC = () => {
  const [data, setData] = useState<HoldingsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchHoldingsData();
  }, []);

  const fetchHoldingsData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getCurrentHoldings();
      setData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch holdings data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(2)}%`;
  };

  const getPnLColor = (pnl: number) => {
    return pnl >= 0 ? 'success.main' : 'error.main';
  };

  const getPerformanceLevel = (percentage: number) => {
    if (percentage >= 10) return { level: 'Excellent', color: 'success' };
    if (percentage >= 5) return { level: 'Good', color: 'info' };
    if (percentage >= 0) return { level: 'Positive', color: 'success' };
    if (percentage >= -5) return { level: 'Moderate Loss', color: 'warning' };
    return { level: 'High Loss', color: 'error' };
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Current Holdings
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchHoldingsData}
        >
          Refresh Holdings
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Holdings Summary */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Holdings
              </Typography>
              <Typography variant="h5">
                {data?.total_holdings || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Market Value
              </Typography>
              <Typography variant="h5">
                {data ? formatCurrency(data.total_market_value) : '₹0'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Unrealized P&L
              </Typography>
              <Typography 
                variant="h5" 
                color={data ? getPnLColor(data.total_unrealized_pnl) : 'text.primary'}
              >
                {data ? formatCurrency(data.total_unrealized_pnl) : '₹0'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Overall Return %
              </Typography>
              <Typography 
                variant="h5"
                color={data && data.total_market_value > 0 ? 
                  getPnLColor((data.total_unrealized_pnl / (data.total_market_value - data.total_unrealized_pnl)) * 100) : 
                  'text.primary'
                }
              >
                {data && data.total_market_value > 0 ? 
                  formatPercentage((data.total_unrealized_pnl / (data.total_market_value - data.total_unrealized_pnl)) * 100) : 
                  '0%'
                }
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Holdings Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Holdings Details
          </Typography>
          
          {data?.holdings && data.holdings.length > 0 ? (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Stock</TableCell>
                    <TableCell>Portfolio</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell align="right">Avg Price</TableCell>
                    <TableCell align="right">Current Price</TableCell>
                    <TableCell align="right">Market Value</TableCell>
                    <TableCell align="right">P&L</TableCell>
                    <TableCell align="right">P&L %</TableCell>
                    <TableCell align="center">Performance</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.holdings.map((holding, index) => {
                    const performance = getPerformanceLevel(holding.unrealized_pnl_percentage);
                    return (
                      <TableRow key={index} hover>
                        <TableCell>
                          <Box>
                            <Typography variant="subtitle2" fontWeight="bold">
                              {holding.stock_symbol}
                            </Typography>
                            <Typography variant="body2" color="textSecondary" noWrap>
                              {holding.stock_name}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {holding.portfolio_name}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          {holding.quantity.toLocaleString()}
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(holding.average_price)}
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(holding.current_price)}
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="subtitle2">
                            {formatCurrency(holding.market_value)}
                          </Typography>
                        </TableCell>
                        <TableCell 
                          align="right"
                          sx={{ color: getPnLColor(holding.unrealized_pnl) }}
                        >
                          <Box display="flex" alignItems="center" justifyContent="flex-end">
                            {holding.unrealized_pnl >= 0 ? <TrendingUp fontSize="small" /> : <TrendingDown fontSize="small" />}
                            <Typography variant="subtitle2" sx={{ ml: 0.5 }}>
                              {formatCurrency(holding.unrealized_pnl)}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell 
                          align="right"
                          sx={{ color: getPnLColor(holding.unrealized_pnl_percentage) }}
                        >
                          <Typography variant="subtitle2">
                            {formatPercentage(holding.unrealized_pnl_percentage)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Box sx={{ width: 100 }}>
                            <Chip
                              label={performance.level}
                              color={performance.color as any}
                              size="small"
                              variant="outlined"
                            />
                            <LinearProgress
                              variant="determinate"
                              value={Math.min(100, Math.max(0, 50 + holding.unrealized_pnl_percentage))}
                              color={performance.color as any}
                              sx={{ mt: 1 }}
                            />
                          </Box>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box textAlign="center" py={4}>
              <HoldingsIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="textSecondary" gutterBottom>
                No Holdings Found
              </Typography>
              <Typography color="textSecondary">
                You don't have any current holdings. Start investing to see your portfolio here.
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Holdings Insights */}
      {data?.holdings && data.holdings.length > 0 && (
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Portfolio Insights
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" color="textSecondary">
                  <strong>Best Performer:</strong> {
                    data.holdings.reduce((best, current) => 
                      current.unrealized_pnl_percentage > best.unrealized_pnl_percentage ? current : best
                    ).stock_symbol
                  } ({formatPercentage(
                    data.holdings.reduce((best, current) => 
                      current.unrealized_pnl_percentage > best.unrealized_pnl_percentage ? current : best
                    ).unrealized_pnl_percentage
                  )})
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" color="textSecondary">
                  <strong>Worst Performer:</strong> {
                    data.holdings.reduce((worst, current) => 
                      current.unrealized_pnl_percentage < worst.unrealized_pnl_percentage ? current : worst
                    ).stock_symbol
                  } ({formatPercentage(
                    data.holdings.reduce((worst, current) => 
                      current.unrealized_pnl_percentage < worst.unrealized_pnl_percentage ? current : worst
                    ).unrealized_pnl_percentage
                  )})
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" color="textSecondary">
                  <strong>Largest Holding:</strong> {
                    data.holdings.reduce((largest, current) => 
                      current.market_value > largest.market_value ? current : largest
                    ).stock_symbol
                  } ({formatCurrency(
                    data.holdings.reduce((largest, current) => 
                      current.market_value > largest.market_value ? current : largest
                    ).market_value
                  )})
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default CurrentHoldings;
