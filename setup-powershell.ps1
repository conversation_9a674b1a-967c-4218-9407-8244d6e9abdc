# PowerShell Setup Script for New Darvas Box Trading Application
Write-Host "🚀 NEW DARVAS BOX TRADING APPLICATION" -ForegroundColor Green
Write-Host "⚡ PowerShell Setup Script" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "README.md")) {
    Write-Host "❌ Please run this script from the project root directory" -ForegroundColor Red
    Write-Host "   (The directory containing README.md)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check Python
Write-Host "🐍 Checking Python..." -ForegroundColor Cyan
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ $pythonVersion found" -ForegroundColor Green
} catch {
    Write-Host "❌ Python not found. Please install Python 3.8+" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Go to backend directory
if (-not (Test-Path "backend")) {
    Write-Host "❌ Backend directory not found!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Set-Location backend

# Create .env file
Write-Host "📝 Creating environment file..." -ForegroundColor Cyan
if (-not (Test-Path ".env")) {
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✅ Created .env from example" -ForegroundColor Green
    } else {
        # Create minimal .env
        @"
DATABASE_URL=sqlite:///./darvas_box.db
SECRET_KEY=dev-secret-key-change-in-production
DEBUG=True
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
"@ | Out-File -FilePath ".env" -Encoding UTF8
        Write-Host "✅ Created minimal .env file" -ForegroundColor Green
    }
} else {
    Write-Host "ℹ️ .env file already exists" -ForegroundColor Yellow
}

# Remove old virtual environment
if (Test-Path "venv") {
    Write-Host "🗑️ Removing old virtual environment..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "venv" -ErrorAction SilentlyContinue
}

# Create virtual environment
Write-Host "📦 Creating Python virtual environment..." -ForegroundColor Cyan
try {
    python -m venv venv
    Write-Host "✅ Virtual environment created" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create virtual environment" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Activate virtual environment
Write-Host "🔄 Activating virtual environment..." -ForegroundColor Cyan
try {
    & ".\venv\Scripts\Activate.ps1"
    Write-Host "✅ Virtual environment activated" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Activation failed, trying alternative method..." -ForegroundColor Yellow
}

# Upgrade pip
Write-Host "⬆️ Upgrading pip..." -ForegroundColor Cyan
& ".\venv\Scripts\python.exe" -m pip install --upgrade pip

# Install core dependencies one by one
Write-Host "📥 Installing core dependencies..." -ForegroundColor Cyan

$corePackages = @(
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0", 
    "sqlalchemy==2.0.23",
    "pydantic==2.5.0",
    "python-dotenv==1.0.0"
)

$successCount = 0
foreach ($package in $corePackages) {
    Write-Host "📦 Installing $package..." -ForegroundColor Cyan
    try {
        & ".\venv\Scripts\pip.exe" install $package
        Write-Host "✅ $package installed" -ForegroundColor Green
        $successCount++
    } catch {
        Write-Host "❌ $package failed" -ForegroundColor Red
    }
}

if ($successCount -ge 3) {
    Write-Host "✅ Core dependencies installed ($successCount/$($corePackages.Count))" -ForegroundColor Green
} else {
    Write-Host "❌ Too many core packages failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Create simple app structure
Write-Host "🏗️ Creating app structure..." -ForegroundColor Cyan

# Create app directory
if (-not (Test-Path "app")) {
    New-Item -ItemType Directory -Path "app" | Out-Null
}

# Create __init__.py files
"" | Out-File -FilePath "app\__init__.py" -Encoding UTF8

# Create simple main.py
$simpleApp = @"
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import os

app = FastAPI(
    title="New Darvas Box Trading API",
    description="Professional Stock Trading and Backtesting Application",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Sample data
sample_data = {
    "portfolio_value": 100000,
    "total_returns": 5000,
    "returns_percentage": 5.0,
    "active_signals": 3,
    "active_gtt_orders": 2,
    "broker_connected": False
}

@app.get("/")
async def root():
    return {
        "message": "New Darvas Box Trading API",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/dashboard/overview")
async def dashboard_overview():
    return {
        "total_portfolio_value": sample_data["portfolio_value"],
        "total_returns": sample_data["total_returns"],
        "returns_percentage": sample_data["returns_percentage"],
        "active_signals": sample_data["active_signals"],
        "active_gtt_orders": sample_data["active_gtt_orders"],
        "broker_connected": sample_data["broker_connected"],
        "portfolios_count": 1,
        "last_updated": datetime.now().isoformat()
    }

@app.get("/api/v1/stocks/search")
async def search_stocks():
    return {
        "stocks": [
            {"symbol": "RELIANCE", "name": "Reliance Industries", "price": 2500.0, "change": 25.0},
            {"symbol": "TCS", "name": "Tata Consultancy Services", "price": 3200.0, "change": -15.0},
            {"symbol": "INFY", "name": "Infosys Limited", "price": 1400.0, "change": 10.0},
            {"symbol": "HDFC", "name": "HDFC Bank", "price": 1600.0, "change": 8.0},
            {"symbol": "ICICIBANK", "name": "ICICI Bank", "price": 950.0, "change": -5.0}
        ],
        "total": 5,
        "page": 1,
        "size": 20
    }

@app.get("/api/v1/portfolio/")
async def get_portfolios():
    return {
        "portfolios": [
            {
                "id": 1,
                "name": "Main Portfolio",
                "initial_capital": 100000,
                "current_value": 105000,
                "cash_balance": 15000,
                "total_returns": 5000,
                "returns_percentage": 5.0,
                "holdings_count": 3
            }
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
"@

$simpleApp | Out-File -FilePath "app\main.py" -Encoding UTF8
Write-Host "✅ Created simplified trading app" -ForegroundColor Green

# Test the installation
Write-Host "🧪 Testing installation..." -ForegroundColor Cyan
try {
    $testResult = & ".\venv\Scripts\python.exe" -c "import fastapi, uvicorn; from app.main import app; print('✅ Installation test passed!')"
    Write-Host $testResult -ForegroundColor Green
    $testPassed = $true
} catch {
    Write-Host "❌ Installation test failed" -ForegroundColor Red
    $testPassed = $false
}

# Go back to root directory
Set-Location ..

if ($testPassed) {
    Write-Host ""
    Write-Host "=" * 60 -ForegroundColor Green
    Write-Host "🎉 SUCCESS! NEW DARVAS BOX TRADING API IS READY!" -ForegroundColor Green
    Write-Host "=" * 60 -ForegroundColor Green
    Write-Host "🚀 To start the server, run:" -ForegroundColor Cyan
    Write-Host "   cd backend" -ForegroundColor White
    Write-Host "   .\venv\Scripts\python.exe -m uvicorn app.main:app --reload" -ForegroundColor White
    Write-Host ""
    Write-Host "📱 Then visit:" -ForegroundColor Cyan
    Write-Host "   http://localhost:8000 - API Home" -ForegroundColor White
    Write-Host "   http://localhost:8000/docs - Interactive API Documentation" -ForegroundColor White
    Write-Host "   http://localhost:8000/api/v1/dashboard/overview - Dashboard Data" -ForegroundColor White
    Write-Host "=" * 60 -ForegroundColor Green
} else {
    Write-Host "❌ Setup completed but with issues. Try running the server manually." -ForegroundColor Red
}

Read-Host "Press Enter to continue"
