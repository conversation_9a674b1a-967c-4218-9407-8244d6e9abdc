import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Dashboard API
export const dashboardApi = {
  getOverview: () => api.get('/dashboard/overview'),
  getCapitalManagement: () => api.get('/dashboard/capital-management'),
  getStockUniversal: () => api.get('/dashboard/stock-universal'),
  getBOHFilter: () => api.get('/dashboard/boh-filter'),
  getWeeklyHigh: () => api.get('/dashboard/weekly-high'),
  getWeeklyHighSignals: () => api.get('/dashboard/weekly-high/signals'),
  getGTTOrders: () => api.get('/dashboard/weekly-high/gtt-orders'),
  getCurrentHoldings: () => api.get('/dashboard/weekly-high/current-holdings'),
  getBacktesting: () => api.get('/dashboard/backtesting'),
  getBrokerConnection: () => api.get('/dashboard/broker-connection'),
  getMarketSummary: () => api.get('/dashboard/market-summary'),
  getPerformanceMetrics: () => api.get('/dashboard/performance-metrics'),
};

// Stocks API
export const stocksApi = {
  searchStocks: (params: any) => api.get('/stocks/search', { params }),
  getStock: (symbol: string) => api.get(`/stocks/${symbol}`),
  createStock: (data: any) => api.post('/stocks/', data),
  updateStock: (symbol: string, data: any) => api.put(`/stocks/${symbol}`, data),
  getPriceHistory: (symbol: string, params: any) => api.get(`/stocks/${symbol}/price-history`, { params }),
  getCurrentPrice: (symbol: string) => api.get(`/stocks/${symbol}/current-price`),
  refreshStockData: (symbol: string) => api.post(`/stocks/${symbol}/refresh-data`),
  getAllStocks: (params: any) => api.get('/stocks/', { params }),
};

// Portfolio API
export const portfolioApi = {
  getPortfolios: (params: any) => api.get('/portfolio/', { params }),
  createPortfolio: (data: any) => api.post('/portfolio/', data),
  getPortfolio: (id: number) => api.get(`/portfolio/${id}`),
  updatePortfolio: (id: number, data: any) => api.put(`/portfolio/${id}`, data),
  deletePortfolio: (id: number) => api.delete(`/portfolio/${id}`),
  getPortfolioSummary: (id: number) => api.get(`/portfolio/${id}/summary`),
  getHoldings: (id: number) => api.get(`/portfolio/${id}/holdings`),
  addHolding: (id: number, data: any) => api.post(`/portfolio/${id}/holdings`, data),
  updateHolding: (portfolioId: number, holdingId: number, data: any) => 
    api.put(`/portfolio/${portfolioId}/holdings/${holdingId}`, data),
  removeHolding: (portfolioId: number, holdingId: number) => 
    api.delete(`/portfolio/${portfolioId}/holdings/${holdingId}`),
  getTransactions: (id: number, params: any) => api.get(`/portfolio/${id}/transactions`, { params }),
  addTransaction: (id: number, data: any) => api.post(`/portfolio/${id}/transactions`, data),
  refreshPortfolio: (id: number) => api.post(`/portfolio/${id}/refresh`),
};

// Strategies API
export const strategiesApi = {
  getStrategies: (params: any) => api.get('/strategies/', { params }),
  createStrategy: (data: any) => api.post('/strategies/', data),
  getStrategy: (id: number) => api.get(`/strategies/${id}`),
  updateStrategy: (id: number, data: any) => api.put(`/strategies/${id}`, data),
  deleteStrategy: (id: number) => api.delete(`/strategies/${id}`),
};

// Backtesting API
export const backtestingApi = {
  getBacktests: (params: any) => api.get('/backtesting/', { params }),
  createBacktest: (data: any) => api.post('/backtesting/', data),
  getBacktest: (id: number) => api.get(`/backtesting/${id}`),
  runBacktest: (id: number) => api.post(`/backtesting/${id}/run`),
  getBacktestResults: (id: number) => api.get(`/backtesting/${id}/results`),
};

// Signals API
export const signalsApi = {
  getSignals: (params: any) => api.get('/signals/', { params }),
  getActiveSignals: () => api.get('/signals/active'),
  generateSignals: () => api.post('/signals/generate'),
  getSignal: (id: number) => api.get(`/signals/${id}`),
};

// Broker API
export const brokerApi = {
  getConnectionStatus: () => api.get('/broker/connection-status'),
  connectBroker: (data: any) => api.post('/broker/connect', data),
  disconnectBroker: () => api.post('/broker/disconnect'),
  getAccountInfo: () => api.get('/broker/account-info'),
  placeOrder: (data: any) => api.post('/broker/place-order', data),
  getOrders: () => api.get('/broker/orders'),
  createGTTOrder: (data: any) => api.post('/broker/gtt-orders', data),
  getGTTOrders: () => api.get('/broker/gtt-orders'),
};

export default api;
