# Development Guide - New Darvas Box Trading Application

## Project Structure

```
new-darvas-box/
├── backend/                    # FastAPI backend
│   ├── app/
│   │   ├── api/               # API endpoints
│   │   ├── core/              # Core configuration
│   │   ├── db/                # Database models and connection
│   │   ├── schemas/           # Pydantic schemas
│   │   ├── services/          # Business logic services
│   │   └── main.py           # FastAPI application
│   ├── alembic/              # Database migrations
│   ├── requirements.txt      # Python dependencies
│   └── Dockerfile           # Docker configuration
├── frontend/                  # React frontend
│   ├── src/
│   │   ├── components/       # Reusable components
│   │   ├── pages/           # Page components
│   │   ├── services/        # API services
│   │   └── utils/           # Utility functions
│   ├── package.json         # Node.js dependencies
│   └── Dockerfile          # Docker configuration
├── smartapi-python-main/     # Angel Broking SmartAPI
├── docker-compose.yml        # Docker Compose configuration
└── README.md                # Project documentation
```

## Development Workflow

### Backend Development

1. **Adding New API Endpoints**
   ```python
   # 1. Create endpoint in app/api/api_v1/endpoints/
   # 2. Add to router in app/api/api_v1/api.py
   # 3. Create corresponding service in app/services/
   # 4. Add Pydantic schemas in app/schemas/
   ```

2. **Database Changes**
   ```bash
   # Create migration
   alembic revision --autogenerate -m "Description"
   
   # Apply migration
   alembic upgrade head
   ```

3. **Adding New Services**
   ```python
   # Create service class in app/services/
   # Inject database session in constructor
   # Implement business logic methods
   ```

### Frontend Development

1. **Adding New Pages**
   ```typescript
   // 1. Create component in src/pages/
   // 2. Add route in src/App.tsx
   // 3. Update navigation in src/components/Layout/Layout.tsx
   ```

2. **API Integration**
   ```typescript
   // 1. Add API methods in src/services/api.ts
   // 2. Use in components with proper error handling
   // 3. Implement loading states
   ```

3. **Styling Guidelines**
   - Use Material-UI components
   - Follow consistent spacing (theme.spacing)
   - Use theme colors and typography
   - Implement responsive design

## Code Standards

### Backend (Python)

- Follow PEP 8 style guide
- Use type hints
- Write docstrings for functions and classes
- Use async/await for I/O operations
- Handle exceptions properly

```python
async def get_stock_data(symbol: str) -> Dict[str, Any]:
    """
    Fetch stock data for given symbol.
    
    Args:
        symbol: Stock symbol (e.g., 'RELIANCE')
        
    Returns:
        Dictionary containing stock data
        
    Raises:
        HTTPException: If stock not found
    """
    try:
        # Implementation
        pass
    except Exception as e:
        logger.error(f"Error fetching stock data: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
```

### Frontend (TypeScript/React)

- Use TypeScript for type safety
- Follow React hooks best practices
- Use functional components
- Implement proper error boundaries
- Use consistent naming conventions

```typescript
interface StockData {
  symbol: string;
  price: number;
  change: number;
}

const StockComponent: React.FC<{ symbol: string }> = ({ symbol }) => {
  const [data, setData] = useState<StockData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStockData();
  }, [symbol]);

  const fetchStockData = async () => {
    try {
      setLoading(true);
      const response = await stocksApi.getStock(symbol);
      setData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <CircularProgress />;
  if (error) return <Alert severity="error">{error}</Alert>;
  if (!data) return <Alert severity="info">No data available</Alert>;

  return (
    <Card>
      <CardContent>
        <Typography variant="h6">{data.symbol}</Typography>
        <Typography variant="h4">{data.price}</Typography>
        <Typography color={data.change >= 0 ? 'success.main' : 'error.main'}>
          {data.change >= 0 ? '+' : ''}{data.change}
        </Typography>
      </CardContent>
    </Card>
  );
};
```

## Testing

### Backend Testing

```python
# test_stock_service.py
import pytest
from app.services.stock_service import StockService

@pytest.fixture
def stock_service():
    # Setup test database session
    return StockService(db_session)

def test_get_stock_data(stock_service):
    result = await stock_service.get_stock_by_symbol("RELIANCE")
    assert result is not None
    assert result.symbol == "RELIANCE"
```

### Frontend Testing

```typescript
// StockComponent.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import StockComponent from './StockComponent';

test('renders stock data', async () => {
  render(<StockComponent symbol="RELIANCE" />);
  
  await waitFor(() => {
    expect(screen.getByText('RELIANCE')).toBeInTheDocument();
  });
});
```

## API Documentation

### Endpoint Structure
- `/api/v1/stocks/` - Stock data operations
- `/api/v1/portfolio/` - Portfolio management
- `/api/v1/dashboard/` - Dashboard data
- `/api/v1/broker/` - Broker integration
- `/api/v1/signals/` - Trading signals
- `/api/v1/backtesting/` - Backtesting operations

### Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Success",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Error Format
```json
{
  "success": false,
  "error": "Error message",
  "detail": "Detailed error information",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Database Schema

### Key Tables
- `users` - User accounts
- `stocks` - Stock information
- `stock_prices` - Historical price data
- `portfolios` - User portfolios
- `holdings` - Portfolio holdings
- `transactions` - Trading transactions
- `strategies` - Trading strategies
- `backtests` - Backtest results
- `signals` - Trading signals
- `gtt_orders` - GTT orders
- `broker_connections` - Broker connections

## Environment Setup

### Development Environment
```bash
# Backend
cd backend
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Frontend
cd frontend
npm install
```

### Production Environment
- Use PostgreSQL instead of SQLite
- Set DEBUG=False
- Use production ASGI server
- Configure proper logging
- Set up monitoring

## Deployment

### Docker Deployment
```bash
docker-compose up -d
```

### Manual Deployment
1. Build frontend: `npm run build`
2. Configure production database
3. Set environment variables
4. Use production server (Gunicorn + Uvicorn)
5. Set up reverse proxy (Nginx)

## Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Make changes following code standards
4. Add tests for new functionality
5. Update documentation
6. Submit pull request

## Performance Optimization

### Backend
- Use database indexing
- Implement caching (Redis)
- Use connection pooling
- Optimize database queries
- Use async operations

### Frontend
- Implement code splitting
- Use React.memo for expensive components
- Optimize bundle size
- Use lazy loading
- Implement proper caching

## Security Considerations

- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection
- Rate limiting
- Authentication and authorization
- Secure credential storage
- HTTPS enforcement
