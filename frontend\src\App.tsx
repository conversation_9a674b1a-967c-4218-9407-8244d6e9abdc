import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box } from '@mui/material';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard/Dashboard';
import CapitalManagement from './pages/CapitalManagement/CapitalManagement';
import StockUniversal from './pages/StockUniversal/StockUniversal';
import BOHFilter from './pages/BOHFilter/BOHFilter';
import WeeklyHigh from './pages/WeeklyHigh/WeeklyHigh';
import SignalGeneration from './pages/WeeklyHigh/SignalGeneration';
import GTTOrderManagement from './pages/WeeklyHigh/GTTOrderManagement';
import CurrentHoldings from './pages/WeeklyHigh/CurrentHoldings';
import BackTesting from './pages/BackTesting/BackTesting';
import ConnectBroker from './pages/ConnectBroker/ConnectBroker';

function App() {
  return (
    <Box sx={{ display: 'flex' }}>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/capital-management" element={<CapitalManagement />} />
          <Route path="/stock-universal" element={<StockUniversal />} />
          <Route path="/boh-filter" element={<BOHFilter />} />
          <Route path="/weekly-high" element={<WeeklyHigh />} />
          <Route path="/weekly-high/signals" element={<SignalGeneration />} />
          <Route path="/weekly-high/gtt-orders" element={<GTTOrderManagement />} />
          <Route path="/weekly-high/holdings" element={<CurrentHoldings />} />
          <Route path="/backtesting" element={<BackTesting />} />
          <Route path="/connect-broker" element={<ConnectBroker />} />
        </Routes>
      </Layout>
    </Box>
  );
}

export default App;
