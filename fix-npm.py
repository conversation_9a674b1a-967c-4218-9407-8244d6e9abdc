#!/usr/bin/env python3
"""
Fix npm installation for Windows
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
from pathlib import Path

def run_command(cmd, shell=True):
    """Run command and return success status"""
    try:
        result = subprocess.run(cmd, shell=shell, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Command output: {result.stdout}")
            print(f"Command error: {result.stderr}")
            return False
        print(f"✅ {result.stdout.strip()}")
        return True
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def check_node_npm():
    """Check Node.js and npm installation"""
    print("🔍 Checking Node.js and npm...")
    
    # Check Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js {result.stdout.strip()} found")
            node_found = True
        else:
            print("❌ Node.js not found")
            node_found = False
    except:
        print("❌ Node.js not found")
        node_found = False
    
    # Check npm
    try:
        result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm {result.stdout.strip()} found")
            npm_found = True
        else:
            print("❌ npm not found")
            npm_found = False
    except:
        print("❌ npm not found")
        npm_found = False
    
    return node_found, npm_found

def fix_npm_windows():
    """Fix npm on Windows"""
    print("🔧 Attempting to fix npm on Windows...")
    
    # Try to find Node.js installation
    possible_paths = [
        r"C:\Program Files\nodejs",
        r"C:\Program Files (x86)\nodejs",
        os.path.expanduser(r"~\AppData\Roaming\npm"),
        os.path.expanduser(r"~\AppData\Local\Programs\nodejs"),
    ]
    
    node_path = None
    for path in possible_paths:
        if os.path.exists(os.path.join(path, "node.exe")):
            node_path = path
            print(f"✅ Found Node.js at: {node_path}")
            break
    
    if not node_path:
        print("❌ Node.js installation not found")
        return False
    
    # Add to PATH
    current_path = os.environ.get("PATH", "")
    if node_path not in current_path:
        print(f"📝 Adding {node_path} to PATH...")
        os.environ["PATH"] = f"{node_path};{current_path}"
    
    # Try npm again
    try:
        result = subprocess.run(["npm", "--version"], capture_output=True, text=True, env=os.environ)
        if result.returncode == 0:
            print(f"✅ npm {result.stdout.strip()} is now working!")
            return True
    except:
        pass
    
    # If npm still doesn't work, try to reinstall it
    print("🔄 Attempting to reinstall npm...")
    npm_path = os.path.join(node_path, "npm.cmd")
    if os.path.exists(npm_path):
        try:
            result = subprocess.run([npm_path, "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ npm {result.stdout.strip()} found at {npm_path}")
                # Create a batch file to use npm
                batch_content = f'@echo off\n"{npm_path}" %*'
                with open("npm.bat", "w") as f:
                    f.write(batch_content)
                print("✅ Created npm.bat wrapper")
                return True
        except:
            pass
    
    return False

def install_nodejs_windows():
    """Download and install Node.js on Windows"""
    print("📥 Downloading Node.js for Windows...")
    
    # Download Node.js installer
    url = "https://nodejs.org/dist/v18.18.0/node-v18.18.0-x64.msi"
    installer_path = "nodejs-installer.msi"
    
    try:
        urllib.request.urlretrieve(url, installer_path)
        print("✅ Node.js installer downloaded")
        
        print("🚀 Installing Node.js...")
        print("⚠️  Please follow the installation wizard and make sure to check 'Add to PATH'")
        
        # Run installer
        os.system(f"msiexec /i {installer_path}")
        
        # Clean up
        if os.path.exists(installer_path):
            os.remove(installer_path)
        
        print("✅ Node.js installation completed")
        print("🔄 Please restart your command prompt and run the script again")
        return True
        
    except Exception as e:
        print(f"❌ Failed to download/install Node.js: {e}")
        return False

def main():
    """Main function"""
    print("🔧 NPM FIX UTILITY")
    print("=" * 30)
    
    node_found, npm_found = check_node_npm()
    
    if node_found and npm_found:
        print("✅ Both Node.js and npm are working!")
        return True
    
    if node_found and not npm_found:
        print("🔧 Node.js found but npm is missing. Attempting to fix...")
        if fix_npm_windows():
            print("✅ npm has been fixed!")
            return True
        else:
            print("❌ Could not fix npm. Reinstalling Node.js...")
            return install_nodejs_windows()
    
    if not node_found:
        print("❌ Node.js not found. Installing...")
        return install_nodejs_windows()

if __name__ == "__main__":
    if main():
        print("\n🎉 SUCCESS! Now you can run:")
        print("python auto-install.py")
    else:
        print("\n❌ Please install Node.js manually from https://nodejs.org")
        print("   Choose the LTS version and make sure to check 'Add to PATH'")
        print("   Then restart your command prompt and run: python auto-install.py")
