# New Darvas Box Trading Application - Installation Guide

## Prerequisites

- Python 3.9 or higher
- Node.js 16 or higher
- npm or yarn
- Git

## Quick Start with Docker (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd new-darvas-box
   ```

2. **Copy environment files**
   ```bash
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   ```

3. **Configure environment variables**
   Edit `backend/.env` and `frontend/.env` with your settings

4. **Start the application**
   ```bash
   docker-compose up -d
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Manual Installation

### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. **Initialize database**
   ```bash
   alembic upgrade head
   ```

6. **Start the backend server**
   ```bash
   uvicorn app.main:app --reload
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

4. **Start the development server**
   ```bash
   npm start
   ```

## SmartAPI Integration Setup

1. **Copy SmartAPI library**
   The SmartAPI Python library should be placed in:
   ```
   smartapi-python-main/smartapi-python-main/
   ```

2. **Install SmartAPI dependencies**
   ```bash
   pip install pyotp logzero websocket-client pycryptodome
   ```

3. **Configure Angel Broking credentials**
   - Obtain API key from Angel Broking developer portal
   - Set up TOTP for 2FA
   - Configure credentials in the Connect Broker section

## Configuration

### Backend Configuration (.env)

```env
# Database
DATABASE_URL=sqlite:///./darvas_box.db

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Angel Broking SmartAPI
ANGEL_API_KEY=your-angel-api-key
ANGEL_CLIENT_CODE=your-client-code
ANGEL_PIN=your-pin
ANGEL_TOTP_SECRET=your-totp-secret

# Application Settings
APP_NAME=New Darvas Box Trading App
APP_VERSION=1.0.0
DEBUG=True

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
```

### Frontend Configuration (.env)

```env
REACT_APP_API_BASE_URL=http://localhost:8000/api/v1
REACT_APP_APP_NAME=New Darvas Box Trading App
REACT_APP_VERSION=1.0.0
```

## Database Setup

### SQLite (Default)
No additional setup required. Database file will be created automatically.

### PostgreSQL (Production)
1. Install PostgreSQL
2. Create database and user
3. Update DATABASE_URL in .env file

## Testing

### Backend Tests
```bash
cd backend
pytest
```

### Frontend Tests
```bash
cd frontend
npm test
```

## Production Deployment

### Using Docker
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Manual Deployment
1. Build frontend:
   ```bash
   cd frontend
   npm run build
   ```

2. Configure production database
3. Set production environment variables
4. Use production ASGI server (Gunicorn + Uvicorn)
5. Set up reverse proxy (Nginx)

## Troubleshooting

### Common Issues

1. **Port already in use**
   - Change ports in docker-compose.yml or stop conflicting services

2. **Database connection errors**
   - Check DATABASE_URL configuration
   - Ensure database server is running

3. **SmartAPI connection issues**
   - Verify API credentials
   - Check TOTP secret configuration
   - Ensure network connectivity

4. **Frontend build errors**
   - Clear node_modules and reinstall: `rm -rf node_modules && npm install`
   - Check Node.js version compatibility

### Logs

- Backend logs: Check console output or configure logging
- Frontend logs: Check browser console
- Docker logs: `docker-compose logs [service-name]`

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review application logs
3. Create an issue in the repository

## Security Notes

- Change default SECRET_KEY in production
- Use environment variables for sensitive data
- Enable HTTPS in production
- Regularly update dependencies
- Secure database access
