from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from app.db.database import get_db
from app.schemas.stock import Stock, StockCreate, StockUpdate, StockSearchResponse
from app.services.stock_service import StockService

router = APIRouter()


@router.get("/search", response_model=StockSearchResponse)
async def search_stocks(
    query: Optional[str] = Query(None, description="Search query for stock symbol or name"),
    exchange: Optional[str] = Query(None, description="Filter by exchange"),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Search stocks with filters and pagination"""
    stock_service = StockService(db)
    return await stock_service.search_stocks(
        query=query,
        exchange=exchange,
        sector=sector,
        page=page,
        size=size
    )


@router.get("/{symbol}", response_model=Stock)
async def get_stock(symbol: str, db: Session = Depends(get_db)):
    """Get stock details by symbol"""
    stock_service = StockService(db)
    stock = await stock_service.get_stock_by_symbol(symbol)
    if not stock:
        raise HTTPException(status_code=404, detail="Stock not found")
    return stock


@router.post("/", response_model=Stock)
async def create_stock(stock: StockCreate, db: Session = Depends(get_db)):
    """Create a new stock"""
    stock_service = StockService(db)
    return await stock_service.create_stock(stock)


@router.put("/{symbol}", response_model=Stock)
async def update_stock(
    symbol: str,
    stock_update: StockUpdate,
    db: Session = Depends(get_db)
):
    """Update stock information"""
    stock_service = StockService(db)
    stock = await stock_service.update_stock(symbol, stock_update)
    if not stock:
        raise HTTPException(status_code=404, detail="Stock not found")
    return stock


@router.get("/{symbol}/price-history")
async def get_price_history(
    symbol: str,
    period: str = Query("1y", description="Period: 1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max"),
    interval: str = Query("1d", description="Interval: 1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo"),
    db: Session = Depends(get_db)
):
    """Get historical price data for a stock"""
    stock_service = StockService(db)
    return await stock_service.get_price_history(symbol, period, interval)


@router.get("/{symbol}/current-price")
async def get_current_price(symbol: str, db: Session = Depends(get_db)):
    """Get current price for a stock"""
    stock_service = StockService(db)
    return await stock_service.get_current_price(symbol)


@router.post("/{symbol}/refresh-data")
async def refresh_stock_data(symbol: str, db: Session = Depends(get_db)):
    """Refresh stock data from Yahoo Finance"""
    stock_service = StockService(db)
    return await stock_service.refresh_stock_data(symbol)


@router.get("/")
async def get_all_stocks(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db)
):
    """Get all stocks with pagination"""
    stock_service = StockService(db)
    return await stock_service.get_all_stocks(skip=skip, limit=limit)
