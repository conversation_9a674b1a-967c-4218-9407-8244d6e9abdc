import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp,
  Refresh as RefreshIcon,
  Notifications as SignalIcon,
  ShoppingCart as OrderIcon,
  Inventory as HoldingsIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { dashboardApi } from '../../services/api';

interface WeeklyHighStock {
  symbol: string;
  name: string;
  current_price: number;
  weekly_high: number;
  distance_from_high: number;
}

interface WeeklyHighData {
  weekly_high_stocks: WeeklyHighStock[];
  total_candidates: number;
  last_updated: string;
}

const WeeklyHigh: React.FC = () => {
  const [data, setData] = useState<WeeklyHighData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchWeeklyHighData();
  }, []);

  const fetchWeeklyHighData = async () => {
    try {
      setLoading(true);
      const response = await dashboardApi.getWeeklyHigh();
      setData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch weekly high data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const getDistanceColor = (distance: number) => {
    if (distance <= 1) return 'success';
    if (distance <= 3) return 'warning';
    return 'default';
  };

  const getStrengthValue = (distance: number) => {
    return Math.max(0, 100 - (distance * 20));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Weekly High Analysis
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={fetchWeeklyHighData}
        >
          Refresh Data
        </Button>
      </Box>

      {/* Quick Actions */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={4}>
          <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/weekly-high/signals')}>
            <CardContent>
              <Box display="flex" alignItems="center">
                <SignalIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h6">Signal Generation</Typography>
                  <Typography color="textSecondary">
                    Generate buy/sell signals based on weekly high analysis
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/weekly-high/gtt-orders')}>
            <CardContent>
              <Box display="flex" alignItems="center">
                <OrderIcon color="warning" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h6">GTT Order Management</Typography>
                  <Typography color="textSecondary">
                    Manage Good Till Triggered orders
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/weekly-high/holdings')}>
            <CardContent>
              <Box display="flex" alignItems="center">
                <HoldingsIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h6">Current Holdings</Typography>
                  <Typography color="textSecondary">
                    View and manage current stock holdings
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Weekly High Candidates */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Weekly High Candidates ({data?.total_candidates || 0})
          </Typography>
          
          {data?.weekly_high_stocks && data.weekly_high_stocks.length > 0 ? (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Symbol</TableCell>
                    <TableCell>Name</TableCell>
                    <TableCell align="right">Current Price</TableCell>
                    <TableCell align="right">Weekly High</TableCell>
                    <TableCell align="right">Distance from High</TableCell>
                    <TableCell align="center">Strength</TableCell>
                    <TableCell align="center">Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.weekly_high_stocks.map((stock) => (
                    <TableRow key={stock.symbol} hover>
                      <TableCell>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {stock.symbol}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" noWrap>
                          {stock.name}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(stock.current_price)}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(stock.weekly_high)}
                      </TableCell>
                      <TableCell align="right">
                        <Chip
                          label={`${stock.distance_from_high.toFixed(2)}%`}
                          size="small"
                          color={getDistanceColor(stock.distance_from_high) as any}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ width: 100 }}>
                          <LinearProgress
                            variant="determinate"
                            value={getStrengthValue(stock.distance_from_high)}
                            color={getDistanceColor(stock.distance_from_high) as any}
                          />
                          <Typography variant="caption" color="textSecondary">
                            {getStrengthValue(stock.distance_from_high).toFixed(0)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        {stock.distance_from_high <= 1 ? (
                          <Chip label="Strong" color="success" size="small" />
                        ) : stock.distance_from_high <= 3 ? (
                          <Chip label="Moderate" color="warning" size="small" />
                        ) : (
                          <Chip label="Weak" color="default" size="small" />
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Box textAlign="center" py={4}>
              <TrendingUp sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="textSecondary" gutterBottom>
                No Weekly High Candidates Found
              </Typography>
              <Typography color="textSecondary">
                No stocks are currently within 5% of their weekly high. Check back later or adjust the criteria.
              </Typography>
            </Box>
          )}
          
          {data?.last_updated && (
            <Typography variant="body2" color="textSecondary" align="center" sx={{ mt: 2 }}>
              Last updated: {new Date(data.last_updated).toLocaleString()}
            </Typography>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default WeeklyHigh;
