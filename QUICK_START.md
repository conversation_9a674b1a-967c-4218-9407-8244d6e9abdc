# 🚀 QUICK START - Just Copy & Paste These Commands

## For Windows Users:

**Step 1:** Open Command Prompt (cmd) and paste this:
```cmd
cd /d C:\Users\<USER>\Documents\Niveshtor ALgo
python run-app.py
```

**If that doesn't work, try this:**
```cmd
cd /d C:\Users\<USER>\Documents\Niveshtor ALgo
setup.bat
start-dev.bat
```

## For Mac/Linux Users:

**Step 1:** Open Terminal and paste this:
```bash
cd "/Users/<USER>/Documents/Niveshtor ALgo"  # Adjust path as needed
python run-app.py
```

**If that doesn't work, try this:**
```bash
cd "/Users/<USER>/Documents/Niveshtor ALgo"  # Adjust path as needed
chmod +x setup.sh start-dev.sh
./setup.sh
./start-dev.sh
```

## What Should Happen:

1. ✅ Scripts will automatically install everything
2. ✅ Database will be created
3. ✅ Both servers will start
4. ✅ Browser should open to http://localhost:3000

## If You See Errors:

**"Python not found":**
- Install Python from https://python.org
- Make sure to check "Add to PATH" during installation

**"Node not found":**
- Install Node.js from https://nodejs.org
- Choose the LTS version

**"Permission denied":**
- On Windows: Run Command Prompt as Administrator
- On Mac/Linux: Add `sudo` before commands if needed

## Still Not Working?

Try the Docker approach (if you have Docker):
```bash
cd "/path/to/your/project"
docker-compose up -d
```

Then go to http://localhost:3000

---

**Just copy one of the command blocks above and paste it into your terminal!**
