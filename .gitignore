# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# PyCharm
.idea/

# VS Code
.vscode/

# Jupyter Notebook
.ipynb_checkpoints

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# React build
/frontend/build/
/frontend/.pnp
/frontend/.pnp.js

# Testing
/frontend/coverage/

# Production
/frontend/build

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
.dockerignore

# Alembic
/backend/alembic/versions/*.py
!/backend/alembic/versions/__init__.py

# Temporary files
*.tmp
*.temp
*.swp
*.swo

# OS generated files
Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Backup files
*.bak
*.backup

# IDE files
*.sublime-project
*.sublime-workspace

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
