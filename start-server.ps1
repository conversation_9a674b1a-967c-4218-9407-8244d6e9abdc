# Start Server Script for New Darvas Box Trading Application
Write-Host "🚀 STARTING NEW DARVAS BOX TRADING SERVER" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "README.md")) {
    Write-Host "❌ Please run this script from the project root directory" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if backend directory exists
if (-not (Test-Path "backend")) {
    Write-Host "❌ Backend directory not found!" -ForegroundColor Red
    Write-Host "Please run setup-powershell.ps1 first" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Go to backend directory
Set-Location backend

# Check if virtual environment exists
if (-not (Test-Path "venv")) {
    Write-Host "❌ Virtual environment not found!" -ForegroundColor Red
    Write-Host "Please run setup-powershell.ps1 first" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if app exists
if (-not (Test-Path "app\main.py")) {
    Write-Host "❌ App not found!" -ForegroundColor Red
    Write-Host "Please run setup-powershell.ps1 first" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if port 8000 is already in use
$portCheck = netstat -an | Select-String ":8000"
if ($portCheck) {
    Write-Host "⚠️ Port 8000 is already in use:" -ForegroundColor Yellow
    Write-Host $portCheck -ForegroundColor Yellow
    Write-Host "Trying to kill existing process..." -ForegroundColor Yellow
    
    # Try to find and kill the process
    $processes = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue
    if ($processes) {
        foreach ($process in $processes) {
            try {
                Stop-Process -Id $process.OwningProcess -Force
                Write-Host "✅ Killed process $($process.OwningProcess)" -ForegroundColor Green
            } catch {
                Write-Host "❌ Could not kill process $($process.OwningProcess)" -ForegroundColor Red
            }
        }
    }
    
    Start-Sleep -Seconds 2
}

# Start the server
Write-Host "🚀 Starting FastAPI server..." -ForegroundColor Cyan
Write-Host "📍 Server will be available at:" -ForegroundColor Cyan
Write-Host "   http://localhost:8000" -ForegroundColor White
Write-Host "   http://localhost:8000/docs" -ForegroundColor White
Write-Host "   http://localhost:8000/api/v1/dashboard/overview" -ForegroundColor White
Write-Host ""
Write-Host "⚠️ Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host "=" * 50 -ForegroundColor Green

try {
    # Start the server
    & ".\venv\Scripts\python.exe" -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
} catch {
    Write-Host "❌ Failed to start server: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Make sure you ran setup-powershell.ps1 first" -ForegroundColor White
    Write-Host "2. Check if Python is installed: python --version" -ForegroundColor White
    Write-Host "3. Check if virtual environment is activated" -ForegroundColor White
    Write-Host ""
    Read-Host "Press Enter to exit"
}
