@echo off
echo 🚀 Starting New Darvas Box Trading Application...

REM Check if setup has been run
if not exist "backend\.env" (
    echo ❌ Environment files not found. Running setup first...
    call setup.bat
)

if not exist "frontend\.env" (
    echo ❌ Environment files not found. Running setup first...
    call setup.bat
)

REM Check if backend dependencies are installed
if not exist "backend\venv" (
    echo ❌ Backend not set up. Running setup first...
    call setup.bat
)

REM Check if frontend dependencies are installed
if not exist "frontend\node_modules" (
    echo ❌ Frontend not set up. Running setup first...
    call setup.bat
)

echo 🔍 Starting servers...

REM Start backend in new window
echo 🐍 Starting backend server...
start "Backend Server" cmd /k "cd backend && venv\Scripts\activate && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend in new window
echo ⚛️  Starting frontend server...
start "Frontend Server" cmd /k "cd frontend && npm run dev"

echo.
echo ✅ Both servers are starting up in separate windows...
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 📚 API Docs: http://localhost:8000/docs
echo.
echo Press any key to continue...
pause >nul
