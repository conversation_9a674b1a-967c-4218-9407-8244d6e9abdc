from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.services.dashboard_service import DashboardService

router = APIRouter()


@router.get("/overview")
async def get_dashboard_overview(db: Session = Depends(get_db)):
    """Get dashboard overview data"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_overview()


@router.get("/capital-management")
async def get_capital_management_data(db: Session = Depends(get_db)):
    """Get capital management dashboard data"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_capital_management_data()


@router.get("/stock-universal")
async def get_stock_universal_data(db: Session = Depends(get_db)):
    """Get stock universal dashboard data"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_stock_universal_data()


@router.get("/boh-filter")
async def get_boh_filter_data(db: Session = Depends(get_db)):
    """Get BOH filter dashboard data"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_boh_filter_data()


@router.get("/weekly-high")
async def get_weekly_high_data(db: Session = Depends(get_db)):
    """Get weekly high dashboard data"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_weekly_high_data()


@router.get("/weekly-high/signals")
async def get_weekly_high_signals(db: Session = Depends(get_db)):
    """Get weekly high signal generation data"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_weekly_high_signals()


@router.get("/weekly-high/gtt-orders")
async def get_gtt_orders(db: Session = Depends(get_db)):
    """Get GTT order management data"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_gtt_orders()


@router.get("/weekly-high/current-holdings")
async def get_current_holdings(db: Session = Depends(get_db)):
    """Get current holdings data"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_current_holdings()


@router.get("/backtesting")
async def get_backtesting_data(db: Session = Depends(get_db)):
    """Get backtesting dashboard data"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_backtesting_data()


@router.get("/broker-connection")
async def get_broker_connection_data(db: Session = Depends(get_db)):
    """Get broker connection dashboard data"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_broker_connection_data()


@router.get("/market-summary")
async def get_market_summary(db: Session = Depends(get_db)):
    """Get market summary for dashboard"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_market_summary()


@router.get("/performance-metrics")
async def get_performance_metrics(db: Session = Depends(get_db)):
    """Get performance metrics for dashboard"""
    dashboard_service = DashboardService(db)
    return await dashboard_service.get_performance_metrics()
