import yfinance as yf
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from app.db.models import Stock, StockPrice
from app.schemas.stock import StockCreate, StockUpdate, StockSearchResponse
import pandas as pd


class StockService:
    def __init__(self, db: Session):
        self.db = db

    async def search_stocks(
        self,
        query: Optional[str] = None,
        exchange: Optional[str] = None,
        sector: Optional[str] = None,
        page: int = 1,
        size: int = 20
    ) -> StockSearchResponse:
        """Search stocks with filters and pagination"""
        query_filter = self.db.query(Stock)
        
        if query:
            query_filter = query_filter.filter(
                or_(
                    Stock.symbol.ilike(f"%{query}%"),
                    Stock.name.ilike(f"%{query}%")
                )
            )
        
        if exchange:
            query_filter = query_filter.filter(Stock.exchange == exchange)
        
        if sector:
            query_filter = query_filter.filter(Stock.sector == sector)
        
        query_filter = query_filter.filter(Stock.is_active == True)
        
        total = query_filter.count()
        stocks = query_filter.offset((page - 1) * size).limit(size).all()
        
        return StockSearchResponse(
            stocks=stocks,
            total=total,
            page=page,
            size=size
        )

    async def get_stock_by_symbol(self, symbol: str) -> Optional[Stock]:
        """Get stock by symbol"""
        return self.db.query(Stock).filter(Stock.symbol == symbol).first()

    async def create_stock(self, stock_data: StockCreate) -> Stock:
        """Create a new stock"""
        db_stock = Stock(**stock_data.dict())
        self.db.add(db_stock)
        self.db.commit()
        self.db.refresh(db_stock)
        return db_stock

    async def update_stock(self, symbol: str, stock_update: StockUpdate) -> Optional[Stock]:
        """Update stock information"""
        db_stock = await self.get_stock_by_symbol(symbol)
        if not db_stock:
            return None
        
        update_data = stock_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_stock, field, value)
        
        self.db.commit()
        self.db.refresh(db_stock)
        return db_stock

    async def get_all_stocks(self, skip: int = 0, limit: int = 100) -> List[Stock]:
        """Get all stocks with pagination"""
        return self.db.query(Stock).filter(Stock.is_active == True).offset(skip).limit(limit).all()

    async def get_price_history(
        self,
        symbol: str,
        period: str = "1y",
        interval: str = "1d"
    ) -> Dict[str, Any]:
        """Get historical price data from Yahoo Finance"""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period, interval=interval)
            
            if hist.empty:
                return {"error": "No data found for symbol"}
            
            # Convert to list of dictionaries for JSON serialization
            data = []
            for date, row in hist.iterrows():
                data.append({
                    "date": date.isoformat(),
                    "open": float(row['Open']),
                    "high": float(row['High']),
                    "low": float(row['Low']),
                    "close": float(row['Close']),
                    "volume": int(row['Volume'])
                })
            
            return {
                "symbol": symbol,
                "period": period,
                "interval": interval,
                "data": data
            }
        
        except Exception as e:
            return {"error": str(e)}

    async def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """Get current price for a stock"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            return {
                "symbol": symbol,
                "current_price": info.get('currentPrice', 0),
                "previous_close": info.get('previousClose', 0),
                "open": info.get('open', 0),
                "day_high": info.get('dayHigh', 0),
                "day_low": info.get('dayLow', 0),
                "volume": info.get('volume', 0),
                "market_cap": info.get('marketCap', 0),
                "pe_ratio": info.get('trailingPE', 0),
                "dividend_yield": info.get('dividendYield', 0),
                "last_updated": datetime.now().isoformat()
            }
        
        except Exception as e:
            return {"error": str(e)}

    async def refresh_stock_data(self, symbol: str) -> Dict[str, Any]:
        """Refresh stock data from Yahoo Finance and update database"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # Update stock information
            db_stock = await self.get_stock_by_symbol(symbol)
            if db_stock:
                db_stock.name = info.get('longName', db_stock.name)
                db_stock.sector = info.get('sector', db_stock.sector)
                db_stock.industry = info.get('industry', db_stock.industry)
                db_stock.market_cap = info.get('marketCap', db_stock.market_cap)
                self.db.commit()
            
            # Get recent price data and store in database
            hist = ticker.history(period="5d", interval="1d")
            for date, row in hist.iterrows():
                existing_price = self.db.query(StockPrice).filter(
                    and_(
                        StockPrice.stock_id == db_stock.id,
                        StockPrice.date == date.date()
                    )
                ).first()
                
                if not existing_price:
                    price_data = StockPrice(
                        stock_id=db_stock.id,
                        date=date,
                        open_price=float(row['Open']),
                        high_price=float(row['High']),
                        low_price=float(row['Low']),
                        close_price=float(row['Close']),
                        volume=int(row['Volume']),
                        adjusted_close=float(row['Close'])
                    )
                    self.db.add(price_data)
            
            self.db.commit()
            
            return {
                "symbol": symbol,
                "message": "Stock data refreshed successfully",
                "last_updated": datetime.now().isoformat()
            }
        
        except Exception as e:
            return {"error": str(e)}
