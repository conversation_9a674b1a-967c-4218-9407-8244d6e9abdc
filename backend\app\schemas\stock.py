from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime


class StockBase(BaseModel):
    symbol: str
    name: str
    exchange: str
    sector: Optional[str] = None
    industry: Optional[str] = None
    market_cap: Optional[float] = None


class StockCreate(StockBase):
    pass


class StockUpdate(BaseModel):
    name: Optional[str] = None
    sector: Optional[str] = None
    industry: Optional[str] = None
    market_cap: Optional[float] = None
    is_active: Optional[bool] = None


class Stock(StockBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class StockPriceBase(BaseModel):
    date: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
    adjusted_close: Optional[float] = None


class StockPriceCreate(StockPriceBase):
    stock_id: int


class StockPrice(StockPriceBase):
    id: int
    stock_id: int
    created_at: datetime

    class Config:
        from_attributes = True


class StockWithPrices(Stock):
    price_data: List[StockPrice] = []


class StockSearchResponse(BaseModel):
    stocks: List[Stock]
    total: int
    page: int
    size: int
